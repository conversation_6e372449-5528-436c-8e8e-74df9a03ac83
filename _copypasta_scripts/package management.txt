// Installer
https://YOURORGURL/packagingSetupUI/ipLanding.app?apvId=04t4P000002XGvbQAG


// Package stuff in SFDX
sfdx package:version:list -v FoglightDevHub --released --created-last-days 5

// Create a package
sfdx force:package:create -n "Foglight Subledger" -t Managed -r force-app -v FoglightDevHub -d "Foglight Subledger"


// Create new package versions
Main App:
sfdx force:package:version:create -v FoglightDevHub --postinstallscript PostInstallHandler --wait 20 --path force-app --definitionfile config/project-scratch-def.json --branch master --tag "r1.2.3" --codecoverage --installationkey "4bg7ew34t6v" --json


// Promote a Package
sfdx force:package:version:promote -v FoglightDevHub -n -p 04t4P0000000000QAA


Set Up User:

sfdx force:user:password:generate --targetusername <username>

