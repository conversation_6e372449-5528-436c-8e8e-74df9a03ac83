/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/16/23.
 */

public with sharing class LWCUtils {
    @AuraEnabled
    public static Map<String,Map<String,List<SelectOption>>> getSelectOptions(Map<String,List<String>> objFieldsReq) {
        Map<String,Map<String,List<SelectOption>>> retVal = new Map<String,Map<String,List<SelectOption>>>();

        for (String objectName : objFieldsReq.keySet()) {
            DescribeSObjectResult objectDSOR = ((SObject) Type.forName(objectName)?.newInstance())?.getSObjectType().getDescribe();

            Map<String,List<SelectOption>> retFieldVals = new Map<String,List<SelectOption>>();
            retVal.put(objectName, retFieldVals);

            for (String fieldName : objFieldsReq.get(objectName)) {
                List<SelectOption> opts = new List<SelectOption>();
                retFieldVals.put(fieldName, opts);

                if (objectDSOR != null) {
                    Map<String, SObjectField> objFields = objectDSOR.fields.getMap();
                    SObjectField sof = objFields?.get(fieldName);
                    if (sof != null) {
                        DescribeFieldResult dfr = sof.getDescribe();
                        switch on (dfr.type) {
                            when PICKLIST {
                                for (PicklistEntry ple : objFields.get(fieldName).getDescribe().getPicklistValues()) {
                                    opts.add(new LWCUtils.SelectOption(ple.label, ple.value));
                                }
                            }
                            when REFERENCE {
                                if ('RecordTypeId'.equalsIgnoreCase(dfr.name)) {
                                    for (RecordTypeInfo rti : objectDSOR.getRecordTypeInfos()) {
                                        opts.add(new LWCUtils.SelectOption(rti));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return retVal;
    }

    /**
     * Get picklist values for a specific object and field
     * Optimized for LWC wire service with cacheable=true
     *
     * @param objectName The API name of the object
     * @param fieldName The API name of the field
     * @return List<SelectOption> List of picklist options
     */
    @AuraEnabled(cacheable=true)
    public static List<SelectOption> getPicklistValuesCached(String objectName, String fieldName) {
        try {
            Schema.SObjectType sObjectType = ((SObject) Type.forName(objectName).newInstance()).getSObjectType();
            return getPickListValues(sObjectType, fieldName);
        } catch (Exception e) {
            System.debug('Error getting picklist values: ' + e.getMessage());
            return new List<SelectOption>();
        }
    }
    public static List<SelectOption> getPickListValues (String objectName, String objectField) {
        Schema.SObjectType sObjectType              = ((SObject) Type.forName(objectName).newInstance()).getSObjectType();
        return getPickListValues(sObjectType, objectField);
    }
    public static List<SelectOption> getPickListValues (SObjectType sot, String objectField) {
        Schema.DescribeSObjectResult dsor           = sot.getDescribe();
        return getPickListValues(dsor, objectField);
    }
    public static List<SelectOption> getPickListValues (DescribeSObjectResult dsor, String objectField) {
        Map<String, Schema.SObjectField> fieldMap   = dsor.fields.getMap();
        return getPickListValues(fieldMap.get(objectField));
    }
    public static List<SelectOption> getPickListValues (SObjectField sof) {
        return getPickListValues(sof.getDescribe());
    }
    public static List<SelectOption> getPickListValues (DescribeFieldResult dfr) {
        List<SelectOption> options                = new List<SelectOption>();

        for (Schema.PicklistEntry a : dfr.getPicklistValues()) {
            options.add(new SelectOption(a.getLabel(), a.getValue()));
        }

        return options;
    }

    public inherited sharing class SelectOption {
        @AuraEnabled
        public String label;
        @AuraEnabled
        public String value;

        public SelectOption(RecordTypeInfo rti) {
            this.label = rti.getName();
            this.value = rti.getRecordTypeId();
        }

        public SelectOption(String labelValueCombo) {
            this.label = labelValueCombo;
            this.value = labelValueCombo;
        }

        public SelectOption(String label, String value) {
            this.label = label;
            this.value = value;
        }

        public SelectOption(Pricebook2 pb2) {
            this.label = pb2.Name;
            this.value = pb2.Id;
        }
    }
}