/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */
@NamespaceAccessible
public without sharing class TriggerSettings {
    private static Map<SObjectType, List<TriggerSetting>> TRIGGER_SETTINGS_MAP;

    static {
        buildMap();
    }

    private static void buildMap () {
        if (TRIGGER_SETTINGS_MAP == null) {

            TRIGGER_SETTINGS_MAP = new Map<SObjectType, List<TriggerSetting>>();
            for (TriggerSetting__mdt ts : [
                    SELECT Id,
                            DeveloperName,
                            Label,
                            ObjectAPIName__c,
                            ApexClassName__c,
                            IsDisabledCompletely__c,
                            IsDisabledBeforeInsert__c,
                            IsDisabledBeforeUpdate__c,
                            IsDisabledBeforeDelete__c,
                            IsDisabledAfterInsert__c,
                            IsDisabledAfterUpdate__c,
                            IsDisabledAfterDelete__c,
                            IsDisabledAfterUndelete__c,
                            IsAsync__c
                    FROM TriggerSetting__mdt
                    ORDER BY Priority__c ASC, DeveloperName ASC
            ]) {
                Type t = Type.forName(null, ts.ObjectAPIName__c);

                if (t != null) {
                    SObject so = (SObject)t.newInstance();

                    if (so != null) {
                        SObjectType sot = so.getSObjectType();

                        if (sot != null) {
                            if (!TRIGGER_SETTINGS_MAP.containsKey(sot)) {
                                TRIGGER_SETTINGS_MAP.put(sot, new List<TriggerSetting>());
                            }
                            TRIGGER_SETTINGS_MAP.get(sot).add(new TriggerSetting(ts));
                        }
                    }
                }
            }
            System.debug(JSON.serializePretty(TRIGGER_SETTINGS_MAP));
        }
    }

    @NamespaceAccessible
    public static List<TriggerSetting> getTriggers (SObjectType sot) {
        List<TriggerSetting> settings = TRIGGER_SETTINGS_MAP.get(sot);
        if (settings == null) {
            settings = new List<TriggerSetting>();
        }
        return settings;
    }

    @NamespaceAccessible
    public class TriggerSetting {
        private TriggerSetting__mdt settingMetaData;
        private Integer suppressCount = 0;

        private TriggerSetting (TriggerSetting__mdt setting) {
            this.settingMetaData = setting;
        }

        @NamespaceAccessible
        public void suppressExecution () {
            suppressCount++;
        }
        @NamespaceAccessible
        public void allowExecution () {
            suppressCount = suppressCount-- < 0 ? 0 : suppressCount;
        }

        @NamespaceAccessible
        public Boolean isSuppressed () {
            return suppressCount > 0;
        }
        @NamespaceAccessible
        public Boolean isDisabled () {
            return settingMetaData.IsDisabledCompletely__c;
        }

        @NamespaceAccessible
        public Boolean isDisabled (System.TriggerOperation to) {
            Boolean val = null;
            if (to != null && settingMetaData != null) {
                String name = 'IsDisabled' + to.name().replace('_', '') + '__c';
                val = (Boolean) settingMetaData.get(name);
                System.debug(name + ': ' + val);
            }

            return isDisabled() || val == true;
        }

        @NamespaceAccessible
        public Domain getDomain () {
            Domain triggerDomain = null;

            Type triggerDomainType = getClass();
            if (triggerDomainType != null) {
                //triggerDomain = (Domain) JSON.deserialize('{}', triggerDomainType); // gets around issues of instantiating something that has no default constructor.
                triggerDomain = (Domain) triggerDomainType.newInstance();
            }

            if (triggerDomain == null) {
                throw new Domain.DomainException(StringUtils.format(System.Label.exception_domain_class_not_found)
                        .set('{className}',String.valueOf(this.settingMetaData.ApexClassName__c))
                        .toString());
            }

            return triggerDomain;
        }

        @NamespaceAccessible
        public String getName () {
            return this.settingMetaData.DeveloperName;
        }

        @NamespaceAccessible
        public Boolean isAsync() {
            return this.settingMetaData.IsAsync__c == true;
        }

        @NamespaceAccessible
        public Type getClass() {
            return TypeUtils.getTypeForClassName(this.settingMetaData.ApexClassName__c);
        }
    }
}