<!--
  ~ Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<!--
 - Created by re<PERSON><PERSON><PERSON> on 1/14/20.
 -->

<!-- Combobox Multi -->
<template>
    <div class="slds-form-element">
        <template if:true={label}>
            <label class="slds-form-element__label">{label}</label>
        </template>
        <div class="slds-form-element__control">
            <div class="slds-combobox_container">

                <div class={dropdownClass}
                     aria-expanded="false"
                     aria-haspopup="listbox"
                     role="combobox">

                    <div class="slds-combobox_form-element slds-input-has-icon slds-input-has-icon_right"
                         role="none">
                        <input class="slds-combobox__input ms-input selected-input"
                               aria-controls="ms-dropdown-items"
                               role="textbox"
                               type="text"
                               onfocus={handleFocus}
                               value={getSelectedItemsDisplay}
                               readonly/>
                        <span class="slds-icon_container slds-icon-utility-down slds-input__icon slds-input__icon_right"
                              title="Click to open the dropdown">
                            <lightning-icon icon-name="utility:down"
                                            size="x-small"
                                            alternative-text="Click here"
                                            class="slds-icon slds-icon--selected slds-icon--x-small slds-icon-text-default slds-m-right--x-small">
                            </lightning-icon>
                        </span>
                    </div>

                    <div class="options-container"
                         style={dropdownStyles}
                         role="listbox">
                        <ul class="slds-listbox slds-listbox_vertical slds-dropdown slds-dropdown_fluid"
                            role="presentation">
                            <template if:true={showFilterInput}>
                                <input class="slds-listbox__item ms-filter-input filter-input"
                                       aria-controls="ms-dropdown-items"
                                       role="textbox"
                                       type="text"
                                       placeholder="Filter values.."
                                       value={_filterValue}
                                       onchange={handleFilterResults}
                                       oninput={handleFilterResults}/>
                            </template>
                            <template if:true={showClearButton}>
                                <lightning-button-icon icon-name="utility:clear"
                                                       alternative-text="Clear current filters"
                                                       class="slds-m-left_xx-small"
                                                       onclick={onClearClick}>
                                </lightning-button-icon>
                            </template>
                            <template if:true={showRefreshButton}>
                                <lightning-button-icon icon-name="utility:refresh"
                                                       alternative-text="Clear all selections and rebuild picklist"
                                                       class="slds-m-left_xx-small"
                                                       onclick={onRefreshClick}>
                                </lightning-button-icon>
                            </template>

                            <template for:each={_options}
                                      for:item="item">
                                <c-combobox-multi-item  key={item.key}
                                                        item={item}
                                                        onoptionselect={handleItemSelected}>
                                </c-combobox-multi-item>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="slds-has-error">
            <div data-help-message="true"
                 role="alert"
                 class={getInputValidityMessage}>
                {validityMessage}
            </div>
        </div>
    </div>

</template>