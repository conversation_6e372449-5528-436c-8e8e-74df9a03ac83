/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 11/16/23.
 */

@IsTest
private class LWCUtilsTest {
    @IsTest
    static void SelectOption() {
        LWCUtils.SelectOption so1 = new LWCUtils.SelectOption('Name','Value');
        System.Assert.areEqual('Name', so1.label);
        System.Assert.areEqual('Value', so1.value);

        RecordTypeInfo rti = SObjectUtils.getRecordTypeInfoForObjectAndName(Campaign.SObjectType, 'default');
        if (rti != null) {
            LWCUtils.SelectOption so2 = new LWCUtils.SelectOption(rti);
            System.Assert.areNotEqual('Default', so2.label);
            System.Assert.areEqual(null, so2.value);
        }

        LWCUtils.SelectOption so3 = new LWCUtils.SelectOption('LabelValue');
        System.Assert.areEqual('LabelValue', so3.label);
        System.Assert.areEqual('LabelValue', so3.value);

        Pricebook2 pb = new Pricebook2(
                Id = IdUtilsTest.generateNextId(Pricebook2.SObjectType),
                Name = 'Test PB'
        );
        LWCUtils.SelectOption so4 = new LWCUtils.SelectOption(pb);
        System.Assert.areEqual(pb.Name, so4.label);
        System.Assert.areEqual(pb.Id, so4.value);
    }

    @IsTest
    static void getSelectOptions() {
        LWCUtils.getSelectOptions(new Map<String,List<String>>{
                'Opportunity' => new List<String>{
                        'Type'
                },
                'Campaign' => new List<String>{
                        'RecordTypeId'
                }
        });
    }

    @IsTest
    static void getPickListValues() {
        LWCUtils.getPickListValues('Opportunity', 'StageName');
    }
}