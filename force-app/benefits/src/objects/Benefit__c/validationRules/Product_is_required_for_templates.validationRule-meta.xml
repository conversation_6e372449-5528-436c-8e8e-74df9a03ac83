<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Product_is_required_for_templates</fullName>
    <active>true</active>
    <description>Product must only be specified for templates.</description>
    <errorConditionFormula>AND(
  RecordType.DeveloperName = &quot;BenefitTemplate&quot;
  , ISBLANK(Product__c)
 )</errorConditionFormula>
    <errorDisplayField>Product__c</errorDisplayField>
    <errorMessage>Product must only be specified for templates.</errorMessage>
</ValidationRule>
