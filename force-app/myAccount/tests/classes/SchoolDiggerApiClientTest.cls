/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@IsTest
private class SchoolDiggerApiClientTest {

	/**
	 * Test the autocompleteSchools method with a successful API response
	 */
	@IsTest
	static void testAutocompleteSchools_Success() {
		String mockResponseBody = '{"schoolMatches":[{"schoolid":"123456","schoolName":"Test Elementary School","city":"San Francisco","state":"CA","zip":"94105"}],"totalCount":1,"numberOfPages":1}';

		Test.setMock(HttpCalloutMock.class, new SchoolDiggerMock(200, mockResponseBody));

		SchoolDiggerApiClient.config = new SchoolDiggerConfig__mdt(
				AppId__c = 'test-app-id',
				AppKey__c = 'test-app-key'
		);

		SchoolDiggerModels.AutocompleteRequest request = new SchoolDiggerModels.AutocompleteRequest();
		request.q = 'Test Elementary';
		request.st = 'CA';

		Test.startTest();
		SchoolDiggerApiClient client = new SchoolDiggerApiClient();
		SchoolDiggerModels.AutocompleteResponse response = client.autocompleteSchools(request);
		Test.stopTest();

		System.assertNotEquals(null, response, 'Response should not be null');
		System.assertEquals(1, response.schoolMatches.size(), 'Should have one school');
		System.assertEquals('123456', response.schoolMatches[0].schoolid, 'School ID should match');
		System.assertEquals('Test Elementary School', response.schoolMatches[0].schoolName, 'School name should match');
		System.assertEquals('San Francisco', response.schoolMatches[0].city, 'City should match');
		System.assertEquals('CA', response.schoolMatches[0].state, 'State should match');
	}

	/**
	 * Test the autocompleteSchools method with an error response
	 */
	@IsTest
	static void testAutocompleteSchools_Error() {
		Test.setMock(HttpCalloutMock.class, new SchoolDiggerMock(400, '{"error":"Invalid request"}'));

		SchoolDiggerApiClient.config = new SchoolDiggerConfig__mdt(
				AppId__c = 'test-app-id',
				AppKey__c = 'test-app-key'
		);

		SchoolDiggerModels.AutocompleteRequest request = new SchoolDiggerModels.AutocompleteRequest();
		request.q = 'Test Elementary';

		Test.startTest();
		SchoolDiggerApiClient client = new SchoolDiggerApiClient();

		try {
			SchoolDiggerModels.AutocompleteResponse response = client.autocompleteSchools(request);
			System.assert(false, 'Should have thrown an exception');
		} catch (SchoolDiggerApiClient.SchoolDiggerApiException e) {
			System.assert(e.getMessage().contains('API request failed: 400'), 'Exception message should contain status code');
		}
		Test.stopTest();
	}

	/**
	 * Test the buildQueryString private method
	 */
	@IsTest
	static void testBuildQueryString() {
		SchoolDiggerApiClient client = new SchoolDiggerApiClient();

		Map<String, String> params = new Map<String, String>{
				'q' => 'Test School',
				'st' => 'CA',
				'city' => 'San Francisco',
				'empty' => null
		};

		Test.startTest();
		SchoolDiggerModels.AutocompleteRequest request = new SchoolDiggerModels.AutocompleteRequest();
		request.q = 'Test School';
		request.st = 'CA';

		Test.setMock(HttpCalloutMock.class, new SchoolDiggerMock(200, '{"schoolMatches":[],"totalCount":0,"numberOfPages":0}'));

		SchoolDiggerApiClient.config = new SchoolDiggerConfig__mdt(
				AppId__c = 'test-app-id',
				AppKey__c = 'test-app-key'
		);

		client.autocompleteSchools(request);
		Test.stopTest();

		System.assert(true, 'Method should execute without error');
	}

	/**
	 * Mock HTTP callout for SchoolDigger API
	 */
	private class SchoolDiggerMock implements HttpCalloutMock {
		private Integer statusCode;
		private String responseBody;

		public SchoolDiggerMock(Integer statusCode, String responseBody) {
			this.statusCode = statusCode;
			this.responseBody = responseBody;
		}

		public HttpResponse respond(HttpRequest request) {
			HttpResponse response = new HttpResponse();
			response.setStatusCode(statusCode);
			response.setBody(responseBody);
			return response;
		}
	}
}
