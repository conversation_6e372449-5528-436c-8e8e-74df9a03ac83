<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>MasterLabel</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>DeveloperName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SortOrder__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Icon__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Label__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsNavBarVisible__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Target__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Subheader__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Component__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsProtected</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>NamespacePrefix</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>MasterLabel</fields>
        <fields>DeveloperName</fields>
        <fields>NamespacePrefix</fields>
        <relatedList>MyAccountUserPersonaNavigation__mdt.Navigation__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
