/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * MyAccountSchoolService
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 3/21/25
 */

global with sharing class MyAccountSchoolService {

	/**
	 * Retrieves a list of schools associated with the current user
	 *
	 * @param sessionToken The user's authentication token
	 * @return List<MyAccountMsgs.AffiliationModel> List of school affiliations
	 * @throws AuraHandledException if user authentication fails
	 */
	@AuraEnabled(Cacheable=true)
	global static List<MyAccountMsgs.AffiliationModel> getSchools(String sessionToken) {
		MyAccountMsgs.UserModel user = MyAccountUserService.getCurrentUser(sessionToken);

		List<MyAccountMsgs.AffiliationModel> schools = new List<MyAccountMsgs.AffiliationModel>();
		for (MyAccountMsgs.AffiliationModel aff : user.affiliations) {
			if (MyAccountConstants.AFFILIATION_TYPE_SCHOOL.equalsIgnoreCase(aff.type)) {
				schools.add(aff);
			}
		}
		return schools;
	}

	/**
	 * Creates a new school record in the system
	 *
	 * @param sessionToken The user's authentication token
	 * @param schoolCreationRequestString JSON string containing school creation details
	 * @return Id The ID of the newly created school record
	 * @throws AuraHandledException if school creation fails or duplicate exists
	 */
	@AuraEnabled
	public static Id createSchool(String sessionToken, String schoolCreationRequestString) {
		MyAccountMsgs.UserModel user = MyAccountUserService.getCurrentUser(sessionToken);
		MyAccountMsgs.AffiliationModel affiliationModel = (MyAccountMsgs.AffiliationModel) JSON.deserialize(schoolCreationRequestString, MyAccountMsgs.AffiliationModel.class);

		try {
			// Check for existing schools with the same name and location
			String schoolName = affiliationModel.name;
			String city = affiliationModel.city;
			String state = affiliationModel.state;

			String dedupeKey = generateDedupeKey(schoolName, city, state);
			if (String.isBlank(dedupeKey)) {
				throw new AuraHandledException('School name, city, and state are required');
			}

			// Query to check for existing schools
			String queryStr = 'SELECT Id FROM Account WHERE Name = :schoolName AND BillingCity = :city';

			// Use appropriate state field based on picklist enablement
			if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
				queryStr += ' AND BillingStateCode = :state';
			} else {
				queryStr += ' AND BillingState = :state';
			}

			queryStr += ' AND Type = \'School\' LIMIT 1';

			List<Account> existingSchools = Database.query(queryStr);

			if (!existingSchools.isEmpty()) {
				throw new AuraHandledException('A school with this name and location already exists');
			}

			Account school = affiliationModel.toOrganization();
			school.Type = 'School';
			school.RecordTypeId = AccountDomain.RT_ORG;

			DB.DMLResult schoolResult = DB.init(false, false, false).create(school);

			if (!schoolResult.isSuccess()) {
				throw new AuraHandledException('Failed to create school: ' + schoolResult.errorMessage);
			}

			return (Id) schoolResult.recordId;

		} catch (Exception e) {
			throw new AuraHandledException('Failed to create school: ' + e.getMessage());
		}
	}

	/**
	 * Searches for schools based on search term and state
	 * Returns combined results from both local database and SchoolDigger API
	 *
	 * @param searchTerm The search string (school name, address, or NCES ID)
	 * @param state Optional state code filter
	 * @return List<MyAccountMsgs.SchoolSearchResult> List of matching schools
	 * @throws AuraHandledException if search operation fails
	 */
	@AuraEnabled(Cacheable=true)
	public static List<MyAccountMsgs.SchoolSearchResult> searchSchools(String searchTerm, String state) {

		List<MyAccountMsgs.SchoolSearchResult> results = new List<MyAccountMsgs.SchoolSearchResult>();
		Map<String, MyAccountMsgs.SchoolSearchResult> dedupeMap = new Map<String, MyAccountMsgs.SchoolSearchResult>();

		Set<String> SCHOOL_FIELDS = new Set<String>{
				'Id',
				'Name',
				'BillingCity',
				'BillingState',
				'BillingPostalCode',
				'SchoolLevel__c',
				'SchoolType__c',
				'SchoolId__c'
		};

		// Add state/country code fields if picklists are enabled
		if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
			SCHOOL_FIELDS.add('BillingStateCode');
			SCHOOL_FIELDS.add('BillingCountryCode');
		} else {
			SCHOOL_FIELDS.add('BillingState');
			SCHOOL_FIELDS.add('BillingCountry');
		}

		String searchQuery = 'SELECT ' + String.join(new List<String>(SCHOOL_FIELDS), ', ') + ' ' +
				'FROM Account ' +
				'WHERE Type = \'School\' AND (' +
				'Name LIKE :searchPattern OR ' +
				'BillingStreet LIKE :searchPattern OR ' +
				'BillingCity LIKE :searchPattern OR ' +
				'SchoolId__c LIKE :searchPattern) ';

		if (String.isNotBlank(state)) {
			if (OrgInfoUtils.isStateAndCountryPicklistsEnabled()) {
				searchQuery += 'AND BillingStateCode = :state ';
			} else {
				searchQuery += 'AND BillingState = :state ';
			}
		}

		String searchPattern = '%' + String.escapeSingleQuotes(searchTerm) + '%';

		List<Account> accounts = DB.init(false, false, false)
				.read(searchQuery)
				.setParam('searchPattern', searchPattern)
				.setParam('state', state)
				.go();

		for (Account acc : accounts) {
			MyAccountMsgs.SchoolSearchResult result = new MyAccountMsgs.SchoolSearchResult(acc);
			if (String.isNotBlank(result.schoolId)) {
				dedupeMap.put('SCHOOLID_' + result.schoolId, result);
			} else {
				String dedupeKey = generateDedupeKey(result.schoolName, result.city, result.state);
				if (String.isNotBlank(dedupeKey)) {
					dedupeMap.put('NAME_' + dedupeKey, result);
				} else {
					results.add(result);
				}
			}
		}

		Boolean enableSchoolDiggerSearch = MyAccountGeneralConfig.isSchoolDiggerSearchEnabled();

		if (enableSchoolDiggerSearch) {
			try {
				SchoolDiggerModels.AutocompleteRequest request = new SchoolDiggerModels.AutocompleteRequest();
				request.q = searchTerm;
				if (String.isNotBlank(state)) {
					request.st = state;
				}
				request.qSearchCityStateName = true;
				request.returnCount = 10;

				SchoolDiggerApiClient client = new SchoolDiggerApiClient();
				SchoolDiggerModels.AutocompleteResponse apiResponse = client.autocompleteSchools(request);

				if (apiResponse?.schoolMatches != null) {
					for (SchoolDiggerModels.SchoolMatch match : apiResponse.schoolMatches) {
						MyAccountMsgs.SchoolSearchResult result = new MyAccountMsgs.SchoolSearchResult(match);
						if (String.isNotBlank(result.schoolId)) {
							if (!dedupeMap.containsKey('SCHOOLID_' + result.schoolId)) {
								dedupeMap.put('SCHOOLID_' + result.schoolId, result);
							}
						} else {
							String dedupeKey = generateDedupeKey(result.schoolName, result.city, result.state);
							if (String.isNotBlank(dedupeKey) && !dedupeMap.containsKey('NAME_' + dedupeKey)) {
								dedupeMap.put('NAME_' + dedupeKey, result);
							} else {
								results.add(result);
							}
						}
					}
				}
			} catch (Exception e) {
				System.debug(LoggingLevel.ERROR, 'Error searching SchoolDigger API: ' + e.getMessage());
			}
		}

		results.addAll(dedupeMap.values());
		return results;
	}

	private static String generateDedupeKey(String name, String city, String state) {
		if (String.isBlank(name) || String.isBlank(city) || String.isBlank(state)) return null;
		return (name + '|' + city + '|' + state).toLowerCase().trim();
	}

	/**
	 * Creates a new affiliation between a user and a school
	 * Handles both existing schools and creation of new external schools
	 *
	 * @param sessionToken The user's authentication token
	 * @param affiliationRequestString JSON string containing affiliation details
	 * @throws AuraHandledException if affiliation creation fails or duplicate exists
	 */
	@AuraEnabled
	public static void createAffiliation(String sessionToken, String affiliationRequestString) {
		MyAccountMsgs.UserModel user = MyAccountUserService.getCurrentUser(sessionToken);
		MyAccountMsgs.SchoolAffiliationRequest request = (MyAccountMsgs.SchoolAffiliationRequest) JSON.deserialize(affiliationRequestString, MyAccountMsgs.SchoolAffiliationRequest.class);

		Id accountId;

		Savepoint sp = Database.setSavepoint();

		try {
			if (String.isBlank(request.schoolId)) {
				throw new AuraHandledException('School ID is required');
			}

			/*if (String.isBlank(request.contactPointEmailId)) {
				throw new AuraHandledException('Email is required');
			}*/

			if (!request.isExternalSchool) {
				List<Account> existingSchools = DB.init(false, false, false)
						.read('SELECT Id ' +
								'FROM Account ' +
								'WHERE Id = :schoolId ' +
								'AND Type = \'School\' ' +
								'LIMIT 1')
						.setParam('schoolId', request.schoolId)
						.go();

				if (existingSchools.isEmpty()) {
					throw new AuraHandledException('School not found in the system');
				}
				accountId = existingSchools[0].Id;
			} else {
				// TODO: determine how we will get nces externally and then update this code to support that change so we can look for schools internally with an external key
				// 	Removing this external school code for now since we don't have a solid idea how nces id will come to us externally.
				/*List<Account> existingSchools = new List<Account>();
				if (String.isNotBlank(request.ncesId)) {
					existingSchools = DB.init(false, false, false)
							.read('SELECT Id ' +
									'FROM Account ' +
									'WHERE (NCESId__c = :ncesId OR SchoolId__c = :schoolId) ' +
									'AND Type = \'School\' ' +
									'LIMIT 1')
							.setParam('ncesId', request.ncesId)
							.setParam('schoolId', request.schoolId)
							.go();
				}

				if (!existingSchools.isEmpty()) {
					accountId = existingSchools[0].Id;
				} else {
					Account newSchool = request.toAccount();

					DB.DMLResult saveResult = DB.init(false, false, false).create(newSchool);

					if (!saveResult.isSuccess()) {
						throw new AuraHandledException('Failed to create school: ' + saveResult.errorMessage);
					}

					accountId = (Id) saveResult.recordId;
				}*/

				// With the above code, we should no longer support external schools passed into here thus we will throw an exception
				throw new AuraHandledException('External schools are not supported');
			}

			Affiliation__c aff = request.toAffiliation(user.Id, accountId);

			if (aff.ContactPointEmail__c == null) {
				List<ContactPointEmail> contactEmails = [
					SELECT Id, IsPrimary, CreatedDate
					FROM ContactPointEmail
					WHERE (ParentId = :user.personAccountId OR Account__c =: user.personAccountId)
					ORDER BY IsPrimary DESC, CreatedDate DESC
				];

				if (!contactEmails.isEmpty()) {
					aff.ContactPointEmail__c = contactEmails[0].Id;
				}
			}

			List<Affiliation__c> existingAffiliations = [
					SELECT Id
					FROM Affiliation__c
					WHERE Contact__c = :user.Id
					AND Organization__c = :accountId
					AND Status__c = 'Current'
					LIMIT 1
			];

			if (!existingAffiliations.isEmpty()) {
				throw new AuraHandledException('You already have an affiliation with this school');
			}

			DB.DMLResult affResult = DB.init(false, false, false).create(aff);

			if (!affResult.isSuccess()) {
				Database.rollback(sp);
				throw new AuraHandledException('Failed to create affiliation: ' + affResult.errorMessage);
			}

		} catch (Exception e) {
			Database.rollback(sp);
			throw new AuraHandledException(e.getMessage());
		}
	}

	/**
	 * Validates if the current user has an active affiliation with the specified school
	 *
	 * @param sessionToken The user's authentication token
	 * @param accountId The Salesforce Id of the school account to validate
	 * @return Boolean True if user has valid affiliation, false otherwise
	 * @throws AuraHandledException if validation fails
	 */
	@AuraEnabled(cacheable=true)
	global static Boolean hasValidSchoolAffiliation(String sessionToken, String accountId) {
		MyAccountMsgs.UserModel user = MyAccountUserService.getCurrentUser(sessionToken);

		if (user == null || String.isBlank(accountId)) {
			return false;
		}

		List<Affiliation__c> affiliations = [
				SELECT Id
				FROM Affiliation__c
				WHERE Contact__c = :user.Id
				AND Organization__c = :accountId
				AND Status__c = 'Current'
				LIMIT 1
		];

		return !affiliations.isEmpty();
	}
}