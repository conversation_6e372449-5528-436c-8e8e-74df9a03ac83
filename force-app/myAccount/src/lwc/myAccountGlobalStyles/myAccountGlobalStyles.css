/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountGlobalStyles
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 2/15/25
 */

/* ===============
   CSS RESET
   =============== */

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
    display: block;
}

body {
    line-height: 1;
}

ol, ul {
    list-style: none;
}

blockquote, q {
    quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

table th {
    border-bottom: 1px solid var(--border-color);
}

table td {
    border-bottom: 1px solid var(--border-color);
}

a {
    text-decoration: none;
}

/* ===============
   HTML
   =============== */

html {
    font-size: var(--dxp-s-html-font-size, 16px) !important;
    font-family: var(--dxp-s-html-font-family, 'Inter', sans-serif);
}

@media (max-width: 768px) {
    html {
        font-size: var(--dxp-s-html-font-size-mobile, 16px) !important;
    }
}

:host {
    /* Base Colors */
    --white: 100% 0 0;

    --black: 0% 0 0;
    --black-light: 40% 0 0;

    --green-light: 73.56% 0.215 147.45;
    --green: 63.56% 0.215 147.45;
    --green-dark: 53.56% 0.215 147.45;

    --gray: 96.76% 0.001 286.38;
    --gray-dark: 86.76% 0.004 286.32;
    --gray-darker: 55.76% 0.001 286.38;

    --red: 50.88% 0.2 29.2338851923426;
    --red-light: 55.88% 0.2 29.2338851923426;

    --yellow: #f8f0c4;
    --yellow-dark: 60% 0.3 85;

    /* Text Colors */
    --text-color: oklch(var(--black));
    --text-color-brand: oklch(var(--black));
    --text-color-inverted: oklch(var(--white));
    --text-color-hovered: oklch(var(--green));
    --text-color-light: oklch(var(--gray-darker));

    --link-color: var(--dxp-s-link-text-color, oklch(var(--green-dark)));
    --link-color-hovered: var(--dxp-s-link-text-color-hover, oklch(var(--green)));

    /* Button Colors */
    --button-brand-color-background: var(--dxp-s-button-color, oklch(var(--black)));
    --button-brand-color-border: var(--dxp-s-button-border-color, oklch(var(--black)));
    --button-brand-color-text: var(--dxp-s-button-color-contrast, oklch(var(--white)));

    --button-brand-color-background-hover: var(--dxp-s-button-color-hover, oklch(var(--black-light)));
    --button-brand-color-border-hover: var(--dxp-s-button-border-color-hover, oklch(var(--black-light)));
    --button-brand-color-text-hover: var(--dxp-s-button-color-hover-contrast, oklch(var(--white)));

    --button-brand-color-background-focus: var(--dxp-s-button-color-focus, oklch(var(--black)));
    --button-brand-color-border-focus: var(--dxp-s-button-border-color-focus, oklch(var(--black)));
    --button-brand-color-text-focus: var(--dxp-s-button-color-focus-contrast, oklch(var(--white)));

    /* Secondary Button (Default/Neutral) */
    --button-color-background: var(--dxp-s-secondary-button-color, oklch(var(--white)));
    --button-color-border: var(--dxp-s-secondary-button-border-color, oklch(var(--gray-dark)));
    --button-color-text: var(--dxp-s-secondary-button-text-color, oklch(var(--black)));

    --button-color-background-hover: var(--dxp-s-secondary-button-color-hover, oklch(var(--gray)));
    --button-color-border-hover: var(--dxp-s-secondary-button-border-color-hover, oklch(var(--black)));
    --button-color-text-hover: var(--dxp-s-secondary-button-text-color-hover, oklch(var(--black)));

    --button-color-background-focus: var(--dxp-s-secondary-button-color-focus, oklch(var(--gray)));
    --button-color-border-focus: var(--dxp-s-secondary-button-border-color-focus, oklch(var(--black)));
    --button-color-text-focus: var(--dxp-s-secondary-button-text-color-focus, oklch(var(--black)));


    /* Tertiary Button (Destructive) */
    --button-destructive-color-background: var(--dxp-s-tertiary-button-color, oklch(var(--red)));
    --button-destructive-color-border: var(--dxp-s-tertiary-button-border-color, oklch(var(--red)));
    --button-destructive-color-text: var(--dxp-s-tertiary-button-text-color, oklch(var(--white)));

    --button-destructive-color-background-hover: var(--dxp-s-tertiary-button-color-hover, oklch(var(--red-light)));
    --button-destructive-color-border-hover: var(--dxp-s-tertiary-button-border-color-hover, oklch(var(--red-light)));
    --button-destructive-color-text-hover: var(--dxp-s-tertiary-button-text-color-hover, oklch(var(--white)));

    --button-destructive-color-background-focus: var(--dxp-s-tertiary-button-color-focus, oklch(var(--red)));
    --button-destructive-color-border-focus: var(--dxp-s-tertiary-button-border-color-focus, oklch(var(--red)));
    --button-destructive-color-text-focus: var(--dxp-s-tertiary-button-text-color-focus, oklch(var(--white)));

    /* Button Typography and Sizing */
    --button-font-family: var(--dxp-s-button-font-family, var(--dxp-s-html-font-family));
    --button-font-style: var(--dxp-s-button-font-style, normal);
    --button-font-weight: var(--dxp-s-button-font-weight, var(--bold));
    --button-text-transform: var(--dxp-s-button-text-transform, none);
    --button-text-decoration: var(--dxp-s-button-text-decoration, none);
    --button-line-height: var(--dxp-s-button-line-height, 1.5);
    --button-letter-spacing: var(--dxp-s-button-letter-spacing, 0);

    /* Standard Button Sizing */
    --button-padding-block-start: var(--dxp-s-button-padding-block-start, 0.5rem);
    --button-padding-block-end: var(--dxp-s-button-padding-block-end, 0.5rem);
    --button-padding-inline-start: var(--dxp-s-button-padding-inline-start, 1.5rem);
    --button-padding-inline-end: var(--dxp-s-button-padding-inline-end, 1.5rem);
    --button-font-size: var(--dxp-s-button-font-size, var(--rfs));
    --button-standard-radius: var(--dxp-s-button-radius-border, 0.5rem);

    /* Small Button Sizing */
    --button-small-padding-block-start: var(--dxp-s-button-small-padding-block-start, 0.25rem);
    --button-small-padding-block-end: var(--dxp-s-button-small-padding-block-end, 0.25rem);
    --button-small-padding-inline-start: var(--dxp-s-button-small-padding-inline-start, 0.75rem);
    --button-small-padding-inline-end: var(--dxp-s-button-small-padding-inline-end, 0.75rem);
    --button-small-font-size: var(--dxp-s-button-small-font-size, var(--sfs));
    --button-small-radius: var(--dxp-s-button-small-radius-border, 0.375rem);

    /* Large Button Sizing */
    --button-large-padding-block-start: var(--dxp-s-button-large-padding-block-start, 0.75rem);
    --button-large-padding-block-end: var(--dxp-s-button-large-padding-block-end, 0.75rem);
    --button-large-padding-inline-start: var(--dxp-s-button-large-padding-inline-start, 2rem);
    --button-large-padding-inline-end: var(--dxp-s-button-large-padding-inline-end, 2rem);
    --button-large-font-size: var(--dxp-s-button-large-font-size, var(--mfs));
    --button-large-radius: var(--dxp-s-button-large-radius-border, 0.625rem);

    /* Background Colors */
    --background-color-green-light: oklch(var(--green-light));
    --background-color-green-dark: oklch(var(--green-dark));
    --background-color-black: oklch(var(--black));
    --background-color-black-light: oklch(var(--black-light));
    --background-color-white: oklch(var(--white));
    --background-color-gray: oklch(var(--gray));
    --background-color-red: oklch(var(--red));
    --background-color-red-light: oklch(var(--red-light));
    --background-color-overlay: oklch(var(--black-light) / 80%);
    --background-color-yellow: var(--yellow);
    --background-color-brand: var(--dxp-s-brand-color, oklch(var(--black)));

    --disabled-background-color: var(--background-color-gray);
    --readonly-background-color: var(--background-color-gray);

    /* Border Colors */
    --border-color: oklch(var(--gray-dark));
    --border-color-focused: oklch(var(--black));
    --border-color-green: oklch(var(--green));
    --line-break-color: oklch(var(--gray-dark));

    /* Radius */
    --radius: var(--button-standard-radius);

    --border-radius-small: calc(var(--radius) - 1px);
    --border-radius-medium: calc(var(--radius) - 2px);
    --border-radius-large: calc(var(--radius) - 4px);

    --danger-color: oklch(var(--red));
    --success-color: oklch(var(--green-dark));
    --pending-color: oklch(var(--yellow));
    --warning-color: oklch(var(--yellow-dark));

    /* Fallback Font Sizes */
    --sfs: .8rem;
    --rfs: .875rem;
    --remfs: 1rem;
    --mfs: 1.25rem;
    --lfs: 1.75rem;
    --xlfs: 2.5rem;
    --xxsfs: 0.625rem;

    /* Fallback Font Weights */
    --bold: 500;
    --normal: 400;
    --light: 300;
    --semibold: 600;

    /* Padding */
    --xsp: .25rem;
    --sp: .5rem;
    --rp: 1rem;
    --mp: 1.5rem;
    --lp: 3.5rem;
    --xlp: 4rem;

    /* Box Shadows */
    --shadow-color: rgba(0,0,0,.1);

    --card-box-shadow: 0 2px 2px 0 var(--shadow-color);
    --button-box-shadow: 0 1px 3px 0 var(--shadow-color),0 1px 2px -1px var(--shadow-color);
    --toggle-box-shadow: 0 1px 3px 0 var(--shadow-color),0 1px 2px -1px var(--shadow-color);
    --dropdown-box-shadow: 0 1px 3px 0 var(--shadow-color),0 1px 2px -1px var(--shadow-color);
    --modal-box-shadow: 0 1px 3px 0 var(--shadow-color),0 1px 2px -1px var(--shadow-color);

    --sds-c-button-brand-color-background-hover: transparent; /* Removes blue hover background */
    --sds-c-button-brand-color-border-hover: transparent; /* Removes blue hover border */
    --sds-c-button-brand-text-color-hover: inherit; /* Optional: Keeps text color same on hover */

    --sds-c-input-text-color-border-hover: transparent; /* Remove border color on hover */
    --sds-c-input-shadow-focus: none; /* Remove blue shadow on focus */
}

/* Typography */

.h1 {
    font-family: var(--dxp-s-text-heading-extra-large-font-family, var(--dxp-s-html-font-family));
    font-size: var(--dxp-s-text-heading-extra-large-font-size, var(--xlfs));
    font-weight: var(--dxp-s-text-heading-extra-large-font-weight, var(--light));
    font-style: var(--dxp-s-text-heading-extra-large-font-style, normal);
    letter-spacing: var(--dxp-s-text-heading-extra-large-letter-spacing, -1px);
    line-height: var(--dxp-s-text-heading-extra-large-line-height, 1.25);
    text-decoration: var(--dxp-s-text-heading-extra-large-text-decoration, none);
    text-transform: var(--dxp-s-text-heading-extra-large-text-transform, none);
}

.h2 {
    font-family: var(--dxp-s-text-heading-large-font-family, var(--dxp-s-html-font-family));
    font-size: var(--dxp-s-text-heading-large-font-size, var(--lfs));
    font-weight: var(--dxp-s-text-heading-large-font-weight, var(--light));
    font-style: var(--dxp-s-text-heading-large-font-style, normal);
    letter-spacing: var(--dxp-s-text-heading-large-letter-spacing, -0.5px);
    line-height: var(--dxp-s-text-heading-large-line-height, 1.25);
    text-decoration: var(--dxp-s-text-heading-large-text-decoration, none);
    text-transform: var(--dxp-s-text-heading-large-text-transform, none);
}

.h3 {
    font-family: var(--dxp-s-text-heading-medium-font-family, var(--dxp-s-html-font-family));
    font-size: var(--dxp-s-text-heading-medium-font-size, var(--mfs));
    font-weight: var(--dxp-s-text-heading-medium-font-weight, var(--light));
    font-style: var(--dxp-s-text-heading-medium-font-style, normal);
    letter-spacing: var(--dxp-s-text-heading-medium-letter-spacing, -0.5px);
    line-height: var(--dxp-s-text-heading-medium-line-height, 1.25);
    text-decoration: var(--dxp-s-text-heading-medium-text-decoration, none);
    text-transform: var(--dxp-s-text-heading-medium-text-transform, none);
}

.h4 {
    font-family: var(--dxp-s-text-heading-small-font-family, var(--dxp-s-html-font-family));
    font-size: var(--dxp-s-text-heading-small-font-size, 1.125rem);
    font-weight: var(--dxp-s-text-heading-small-font-weight, var(--light));
    font-style: var(--dxp-s-text-heading-small-font-style, normal);
    letter-spacing: var(--dxp-s-text-heading-small-letter-spacing, 0);
    line-height: var(--dxp-s-text-heading-small-line-height, 1.25);
    text-decoration: var(--dxp-s-text-heading-small-text-decoration, none);
    text-transform: var(--dxp-s-text-heading-small-text-transform, none);
}

.h5 {
    font-family: var(--dxp-s-text-heading-extra-small-font-family, var(--dxp-s-html-font-family));
    font-size: var(--dxp-s-text-heading-extra-small-font-size, 0.8125rem);
    font-weight: var(--dxp-s-text-heading-extra-small-font-weight, var(--semibold));
    font-style: var(--dxp-s-text-heading-extra-small-font-style, normal);
    letter-spacing: var(--dxp-s-text-heading-extra-small-letter-spacing, 0);
    line-height: var(--dxp-s-text-heading-extra-small-line-height, 1.25);
    text-decoration: var(--dxp-s-text-heading-extra-small-text-decoration, none);
    text-transform: var(--dxp-s-text-heading-extra-small-text-transform, none);
}

.h6 {
    font-family: var(--dxp-s-text-heading-extra-extra-small-font-family, var(--dxp-s-html-font-family));
    font-size: var(--dxp-s-text-heading-extra-extra-small-font-size, var(--xxsfs));
    font-weight: var(--dxp-s-text-heading-extra-extra-small-font-weight, var(--semibold));
    font-style: var(--dxp-s-text-heading-extra-extra-small-font-style, normal);
    letter-spacing: var(--dxp-s-text-heading-extra-extra-small-letter-spacing, 0);
    line-height: var(--dxp-s-text-heading-extra-extra-small-line-height, 1.25);
    text-decoration: var(--dxp-s-text-heading-extra-extra-small-text-decoration, none);
    text-transform: var(--dxp-s-text-heading-extra-extra-small-text-transform, none);
}

.p {
    font-family: var(--dxp-s-body-font-family, var(--dxp-s-html-font-family));
    font-size: var(--dxp-s-body-font-size, var(--remfs));
    font-weight: var(--dxp-s-body-font-weight, var(--normal));
    font-style: var(--dxp-s-body-font-style, normal);
    letter-spacing: var(--dxp-s-body-letter-spacing, -0.2px);
    line-height: var(--dxp-s-body-line-height, 1.5);
    text-decoration: var(--dxp-s-body-text-decoration, none);
    text-transform: var(--dxp-s-body-text-transform, none);
    color: var(--p-color, var(--text-color-light));
}

p {
    overflow-wrap: anywhere;
}

.p--small {
    font-family: var(--dxp-s-body-small-font-family, var(--dxp-s-html-font-family));
    font-size: var(--dxp-s-body-small-font-size, var(--sfs));
    font-weight: var(--dxp-s-body-small-font-weight, var(--normal));
    font-style: var(--dxp-s-body-small-font-style, normal);
    letter-spacing: var(--dxp-s-body-small-letter-spacing, 0);
    line-height: var(--dxp-s-body-small-line-height, 1.25);
    text-decoration: var(--dxp-s-body-small-text-decoration, none);
    text-transform: var(--dxp-s-body-small-text-transform, none);
}

.p--black {
    --p-color: var(--text-color);
}

a {
    font-size: var(--font-size, var(--rfs));
    color: var(--color, var(--link-color));
    cursor: pointer;
    text-decoration: var(--dxp-s-link-text-decoration, none);
}

a:hover {
    color: var(--link-color-hovered);
    text-decoration: var(--dxp-s-link-text-decoration-hover, underline);
}

a:focus {
    text-decoration: var(--dxp-s-link-text-decoration-focus, underline);
}

.a {
    color: var(--color, var(--link-color));
}

*.disabled {
    cursor: not-allowed;
}

/* Borders */

.border {
    border: 1px solid var(--border-color, var(--border-color));
}

.border-top {
    border-block-start: 1px solid var(--border-color, var(--border-color));
}

.border-bottom {
    border-block-end: 1px solid var(--border-color, var(--border-color));
}

.border-left {
    border-inline-start: 1px solid var(--border-color, var(--border-color));
}

.border-right {
    border-inline-end: 1px solid var(--border-color, var(--border-color));
}

.border-hover:hover {
    --border-color: var(--border-color-focused);
}

.border-dashed {
    border: 1px dashed var(--border-color);
}

.border-collapse--separate {
    border-collapse: separate;
}

/* Input Fields */

.input {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-medium);
}

.input:focus {
    border: 1px solid var(--border-color-focused);
}

/* ===============
   IMAGES
   =============== */

.line-break {
    border-width: initial;
    border-style: none;
    border-color: initial;
    border-image: initial;
    width: 100%;
    height: 1px;
    margin: 1rem 0px;
    background-color: var(--line-break-color);
}

/* ===============
   FORMS
   =============== */

textarea {
    color: var(--text-color-light);
    font-size: var(--sfs);
    line-height: var(--slh);
    width: 100%;
    min-height: 200px;
    padding: var(--rp);
    box-sizing: border-box;
    font-family: sans-serif, Inter;
    border: 1px solid var(--border-color);
}

input[type=checkbox] {
    transform: scale(.8);
    width: fit-content;
}

.field-height {
    min-height: 48px;
}

.button-reset {
    padding: 0px;
    background: none;
    border: none;
}

.formatted-rich-text p ,
.formatted-rich-text ul li ,
.formatted-rich-text li {
    font-size: var(--rfs);
    color: var(--text-color);
}

/* ===============
   TABLES
   =============== */

.table-fixed {
    table-layout: fixed;
}

.headerRow {
    color: var(--text-color-light);
    height: var(--lp);
    grid-auto-flow: column;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-color-white);
}

.headerRow p {
    font-size: var(--rfs);
}

.bodyRow {
    height: var(--lp);
    border-bottom: 1px solid var(--border-color);
}

.bodyRow p {
    font-size: var(--rfs);
    color: var(--text-color);
}

.bodyRow td {
    vertical-align: middle;
}

.bodyRowCell {
    overflow: hidden;
}

.bodyRowCell p {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bodyRowCell > p {
    margin-inline-end: var(--sp);
}

.headerRowDense {
    border-top: 1px solid var(--third-neutral-color);
    color: var(--secondary-neutral-color);
    height: 2rem;
}

.headerRowDense p {
    font-size: var(--sfs);
}

.headerRowDense > div {
    height: 100%;
    display: flex;
    align-items: center;
    padding-inline-start: var(--rp);
}

.bodyRowDense {
    height: 3rem;
}

.bodyRowDense .bodyRowCell p:first-child {
    color: var(--fifth-neutral-color);
    font-size: var(--sfs);
}

.bodyRowDense .bodyRowCell .secondary-neutral-color {
    color: var(--secondary-neutral-color) !important;
}

.bodyRowDense .bodyRowCell .secondary-neutral-color:first-child:hover {
    color: var(--primary-color) !important;
}
.bodyRowDense > div {
    height: 100%;
    display: flex;
    align-items: center;
    padding-inline-start: var(--rp);
}

.columnContent p,
.columnContent img {
    cursor: pointer;
}

.columnContent:hover {
    color: var(--primary-color);
}

.column {
    cursor: pointer;
    vertical-align: middle;
}

.column-fixed-right {
    right: 0px;
    width: 160px;
    border-left: 2px solid var(--neutral-color);
}

.shadow-bottom {
    -webkit-box-shadow: var(--card-box-shadow);
    box-shadow: var(--card-box-shadow);
}

/* Forms */

.form-label {
    font-size: var(--sfs);
    color: var(--text-color-light);
}

.form-input {
    position: relative;
    padding: 8px 12px 8px 12px;
    height: 50px;
    width: 100%;
    outline: 0;
    border-radius: 0;
    border-width: 1px;
    border-style: solid;
    border-color: var(--border-color);
    transition: border 150ms ease-out;
    color: var(--text-color);
}

.options-container {
    border: 1px solid var(--border-color);
    z-index: 999;
}

.fieldPadding > span:first-of-type {
    display: flex;
    align-items: center;
}

/* ===============
   ANIMATIONS
   =============== */

.transition-25 img {
    transition: transform 0.25s ease-in-out;
}

.transition-25-min-width {
    transition: min-width .25s ease-in-out;
}

.rotate-horizontal {
    rotate: 180deg;
}

/* ===============
   HEIGHT & WIDTH
   =============== */

.width-fit-content {
    width: fit-content;
}

.grid-item-center {
    grid-column: 1/-1;
    grid-row: 1/-1;
}

.backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.pointer-events-none {
    pointer-events: none;
}

.scroll {
    background-image: linear-gradient(rgba(238, 241, 244, 0), rgba(237, 239, 241, 0.555));
    background-size: 100% 14px;
    height: 14px;
    opacity: 1;
    pointer-events: none;
    position: absolute;
    bottom: 0px;
    left: 0px;
    right: 0px;
    border-bottom: 1px solid var(--background-color-gray);
}

.blur {
    display: block;
    -moz-filter: blur(5px);
    -o-filter: blur(5px);
    -ms-filter: blur(5px);
    backdrop-filter: blur(5px);
    background-color: rgba(255, 255, 255, 1);
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
}

/* ===============
   Utilities
   =============== */

.input-actions {
    right: var(--rp);
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: var(--xsp);
}

.error {
    border-color: var(--danger-color) !important;
}

.error-message {
    font-size: var(--sfs);
}

/* Font */

.font-weight--normal {
    font-weight: var(--normal);
}

.font-weight--bold {
    font-weight: var(--bold);
}

/* Backgrounds */

.background--white {
    background-color: var(--background-color-white);
}

.background--black {
    background-color: var(--background-color-black);
}

.background--gray {
    background-color: var(--background-color-gray);
}

.background--yellow {
    background-color: var(--background-color-yellow);
}

/* Color */
.color--light {
    color: var(--text-color-light);
}

.color--error {
    color: var(--danger-color) !important;
}

.color-success {
    color: var(--success-color) !important;
    font-weight: var(--bold);
}

.color-warning {
    color: var(--warning-color) !important;
    font-weight: var(--bold);
}

.color-neutral {
    color: var(--text-color-light);
}

/* Borders */

.border {
    border: 1px solid var(--border-color);
}

.border--urgent {
    border: 1px solid var(--background-color-red-light);
}

/* Border Radius */

.border-radius--small {
    border-radius: var(--border-radius-small);
}

.border-radius--medium {
    border-radius: var(--border-radius-medium);
}

.border-radius--large {
    border-radius: var(--border-radius-large);
}

.border-radius--circle {
    border-radius: 50%;
}

/* Blocks */

.inline-block {
    display: inline-block;
}

.inline-flex {
    display: inline-flex;
}

.block {
    display: block;
}

.border-box {
    box-sizing: border-box;
}

.pointer {
    cursor: pointer;
}

.cursor-default {
    cursor: default;
}

.text-align-center {
    text-align: center;
}

.text-align-left {
    text-align: left;
}

.text-align-right {
    text-align: right;
}

.flex {
    display: flex;
}

.flex-gap-xsp {
    gap: var(--xsp);
    align-items: center;
}

.flex-gap-sp {
    gap: var(--sp);
    align-items: center;
}

.flex-gap-rp {
    gap: var(--rp);
    align-items: center;
}

.flex-gap-mp {
    gap: var(--mp);
    align-items: center;
}

.flex-gap-lp {
    gap: var(--lp);
    align-items: center;
}

.flex-direction-row {
    flex-direction: row;
}

.flex-direction-column {
    flex-direction: column;
}

.flex-direction-row-reverse {
    flex-direction: row-reverse;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

.stretch {
    flex: 1 1;
}

.space-between {
    justify-content: space-between;
}

.align-items-flex-start {
    align-items: flex-start;
}

.align-items-flex-end {
    align-items: flex-end;
}

.align-items-center {
    align-items: center;
}

.align-items-flex-stretch {
    align-items: stretch;
}

.align-self-flex-end {
    align-self: flex-end;
}

.align-self-center {
    align-self: center;
}

.justify-content-center {
    justify-content: center;
}

.justify-content-flex-start {
    justify-content: flex-start;
}

.justify-content-flex-end {
    justify-content: flex-end;
}

.center {
    justify-content: center;
    align-items: center;
}

.flex-grow-shrink-1 {
    flex: 1;
}

.flex-basis-20vh {
    flex-basis: 20vh;
}

.grid {
    display: grid;
}

.grid-columns-two {
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: var(--sp);
}

.grid-columns-three {
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: var(--sp);
}

.grid-columns-four {
    grid-template-columns: repeat(4, 1fr);
    grid-column-gap: var(--rp);
}

.all-columns {
    grid-column: 1/-1;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.relative {
    position: relative;
}

.sticky {
    position: sticky;
}

.top-sticky {
    position: sticky;
    top: 0px;
    z-index: 2;
}

.left-sticky {
    position: sticky;
    left: 0;
}

.right-sticky {
    position: sticky;
    right: 0;
    z-index: 1;
}

td.right-sticky:focus-within {
    z-index: 2 !important;
}

.inset-0 {
    inset: 0;
}

.top {
    top: 0;
}

.right {
    right: 0;
}

.bottom-sticky {
    position: sticky;
    bottom: 0px;
    border-width: 1px;
    border-style: solid;
    border-color: rgb(232, 232, 220);
    border-image: initial;
    border-left: none;
    border-right: none;
    border-bottom: none;
    z-index: 1;
}

.inherit-width {
    width: inherit;
}

.width-fit-content {
    width: fit-content;
}

.full-width {
    width: 100%;
}

.three-quarters-width {
    width: 75%;
}

.half-width {
    width: 50%;
}

.quarter-width {
    width: 25%;
}

.width-large {
    width: 1200px;
}

.width-medium {
    width: 750px;
}

.width-small {
    width: 550px;
}

.width-xsmall {
    width: 275px;
}

.width-xxsmall {
    width: 125px;
}

.width-90vw {
    width: 90vw;
}

.min-width-xxsmall {
    min-width: 125px;
}

.mini-nav-width {
    min-width: 4.2em;
}

.min-width-medium {
    min-width: 750px;
}

.nav-width {
    min-width: 16rem;
}

.search-dimensions {
    width: 15rem;
    height: 1.5rem;
}

.full-height {
    height: 100%;
    overflow: hidden;
}

.height-100 {
    height: 100%;
}

.full-viewport-height {
    height: 100vh;
    overflow: hidden;
}

.height-60px {
    height: 60px;
}

.height-100vh {
    height: 100vh;
}

.height-75vh {
    height: 75vh;
}

.height-50vh {
    height: 50vh;
}

.height-medium {
    height: 39vh;
    overflow: scroll;
}

.height-fit-content {
    height: fit-content;
}

.max-width-90vw {
    max-width: 90vw;
}

.max-height-medium {
    max-height: 39vh;
    overflow: scroll;
}

.max-height-large {
    max-height: 50vh;
    overflow: scroll;
}

.max-height-xlarge {
    max-height: 70vh;
    overflow: scroll;
}

.min-height-medium {
    min-height: 39vh;
}

.min-height-50vh {
    min-height: 50vh;
}

.min-height-70vh {
    min-height: 70vh;
}

.min-height-100vh {
    min-height: 100vh;
}

.min-height-100pc {
    min-height: 100%;
}

.max-height-100vh {
    max-height: 100vh;
}

.max-height-100 {
    max-height: 100%;
}

.margin-center {
    margin-inline-start: auto;
    margin-inline-end: auto;
}

.margin-inline-start {
    margin-inline-start: auto;
}

.margin-inline-end {
    margin-inline-end: auto;
}

.circle-dimensions {
    width: 20px;
    height: 20px;
}

.height-medium-scroll {
    height: 60vh;
    overflow: auto;
}

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.overflow-auto {
    overflow: auto;
}

.overflow-visible {
    overflow: visible;
}

.overflow-hidden {
    overflow: hidden;
}

.overflow-x-scroll {
    overflow-x: scroll;
}

.overflow-y-scroll {
    overflow-y: scroll;
}

.overflow-x-auto {
    overflow-x: auto;
}

.overflow-y-auto {
    overflow-y: auto;
}

.overflow-x-hidden {
    overflow-x: hidden;
}

.overflow-y-hidden {
    overflow-y: hidden;
}

.overflow-scroll {
    overflow:scroll;
}

.overscroll-contain {
    overscroll-behavior: contain;
}

.text-overflow-ellipsis {
    text-overflow: ellipsis;
}

.z-index-999 {
    z-index: 999;
}

.z-index-998 {
    z-index: 998;
}

.z-index-997 {
    z-index: 997;
}

.icon-container {
    filter: opacity(0.7);
    display: flex;
    align-items: center;
}

.separator {
    background: var(--background-color-black);
    height: 1px;
    opacity: 0.07;
}

.truncate-overflow {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.max-width-11em {
    max-width: 11em;
}

.max-width-9em {
    max-width: 9em;
}

.max-width-7em {
    max-width: 7em;
}

/* =============================
   Utilities - Padding
   ============================= */

.ptxl {
    padding-block-start: var(--xlp)
}

.mtxl {
    margin-block-start: var(--xlp);
}

.prxl {
    padding-inline-end: var(--xlp)
}

.mrxl {
    margin-inline-end: var(--xlp);
}

.pbxl {
    padding-block-end: var(--xlp)
}

.mbxl {
    margin-block-end: var(--xlp);
}

.plxl {
    padding-inline-start: var(--xlp)
}

.mlxl {
    margin-inline-start: var(--xlp);
}

.ptl {
    padding-block-start: var(--lp)
}

.mtl {
    margin-block-start: var(--lp);
}

.prl {
    padding-inline-end: var(--lp);
}

.mrl {
    margin-inline-end: var(--lp);
}

.pbl {
    padding-block-end: var(--lp);
}

.mbl {
    margin-block-end: var(--lp);
}

.pll {
    padding-inline-start: var(--lp);
}

.mll {
    margin-inline-start: var(--lp);
}

.ptm {
    padding-block-start: var(--mp);
}

.mtm {
    margin-block-start: var(--mp);
}

.prm {
    padding-inline-end: var(--mp);
}

.mrm {
    margin-inline-end: var(--mp);
}

.pbm {
    padding-block-end: var(--mp);
}

.mbm {
    margin-block-end: var(--mp);
}

.plm {
    padding-inline-start: var(--mp);
}

.mlm {
    margin-inline-start: var(--mp);
}

.ptr {
    padding-block-start: var(--rp);
}

.mtr {
    margin-block-start: var(--rp);
}

.prr {
    padding-inline-end: var(--rp);
}

.mrr {
    margin-inline-end: var(--rp);
}

.pbr {
    padding-block-end: var(--rp);
}

.mbr {
    margin-block-end: var(--rp)
}

.plr {
    padding-inline-start: var(--rp);
}

.mlr {
    margin-inline-start: var(--rp);
}

.pts {
    padding-block-start: var(--sp);
}

.mts {
    margin-block-start: var(--sp);
}

.prs {
    padding-inline-end: var(--sp);
}

.mrs {
    margin-inline-end: var(--sp)
}

.pbs {
    padding-block-end: var(--sp);
}

.mbs {
    margin-block-end: var(--sp);
}


.pls {
    padding-inline-start: var(--sp);
}

.mls {
    margin-inline-start: var(--sp);
}

.ptxs {
    padding-block-start: var(--xsp);
}

.mtxs {
    margin-block-start: var(--xsp);
}

.prxs {
    padding-inline-end: var(--xsp);
}

.mrxs {
    margin-inline-end: var(--xsp);
}

.pbxs {
    padding-block-end: var(--xsp);
}

.mbxs {
    margin-block-end: var(--xsp);
}

.plxs {
    padding-inline-start: var(--xsp);
}

.mlxs {
    margin-inline-start: var(--xsp);
}

::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 3px;
    height: 3px;
}

::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background-color: rgba(0,0,0,.5);
    -webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);
}

.site-logo {
    background-image: var(--dxp-s-site-logo-url, url(''));
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
    width: 200px;
    height: 50px;
}

@media (max-width: 768px) {
    .site-logo {
        width: 150px;
        height: 40px;
    }
}
