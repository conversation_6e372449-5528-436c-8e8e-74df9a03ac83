/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountTable
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 3/21/25
 */
import {api, LightningElement, track} from 'lwc';
import {classSet} from 'c/utils';

export default class MyAccountTable extends LightningElement {

    _renderLimit = 100;
    _renderIncrement = 50;
    _loading = false;
    showTableShadow = false;

    @api columns;
    @api header;
    @api allRecordsLoaded;
    @api tableStyleClasses;
    @api tableContainerStyleClasses;
    @api lazyLoading;

    @track _records = [];
    @track _draftValues = [];
    @track _renderedRecords = [];
    @track sortArrow = {};
    @track queryParams = {sortedBy: 'CreatedDate', sortDirection: 'desc'};

    @api
    get records() {
        return this.lazyLoading ? this._renderedRecords : this._records;
    }

    /**
     * The title to display when there are no records
     * @default null
     */
    @api emptyStateTitle = null;

    /**
     * The message to display when there are no records
     * @default null
     */
    @api emptyStateMessage = null;

    /**
     * The icon to display when there are no records
     * @default null
     */
    @api emptyStateIcon = null;

    /**
     * Determines if the empty state should be shown based on whether an empty state title was provided
     * and if there are no records to display
     * @returns {boolean} True if the empty state should be shown
     */
    get showEmptyState() {
        return this.emptyStateTitle &&
            (!this.records || this.records.length === 0) &&
            !this.loading;
    }

    set records(value) {
        this._records = value;
        this._renderedRecords = this._records.slice(0, this.renderLimit);
    }

    @api
    get draftValues() {
        return this._draftValues;
    }

    set draftValues(value) {
        this._draftValues = [...value];
    }

    @api
    get loading() {
        return this._loading;
    }

    set loading(value) {
        this._loading = value;
    }

    @api
    get renderLimit() {
        return this._renderLimit;
    }

    set renderLimit(value) {
        this._renderLimit = value;
    }

    @api
    get renderIncrement() {
        return this._renderIncrement;
    }

    set renderIncrement(value) {
        this._renderIncrement = value;
    }

    get sortArrowIcon() {
        return this.queryParams?.sortDirection === 'asc' ? '↑' : '↓'
    }

    get rowClasses() {
        return classSet('full-width')
            .toString();
    }

    get tableClasses() {
        return classSet('table-fixed full-width')
            .add({
                [this.tableStyleClasses]: this.tableStyleClasses
            })
            .toString();
    }

    get tableContainerClasses() {
        return classSet('relative full-width overflow-auto')
            .add({
                [this.tableContainerStyleClasses]: this.tableContainerStyleClasses
            })
            .toString();
    }

    get tableContainerParentClasses() {
        return classSet('relative full-width')
            .add({
                [this.tableContainerStyleClasses]: this.tableContainerStyleClasses
            })
            .toString();
    }

    @api
    resetScroll() {
        const table = this.refs.table;
        if (table)
            table.scrollTop = 0;
    }

    renderedCallback() {
        if (this.refs.table) {
            this.setOverflowStyles(this.refs.table);
        }

        this.dispatchEvent(
            new CustomEvent(
                'rendered',
                {
                    bubbles: false,
                    composed: false,
                    detail: {
                        renderedRecordCount: this._renderedRecords.length
                    }
                }
            )
        );
    }

    /**
     * Description: handles initiating the table
     * @param event
     */
    handleSort(event) {
        event.stopPropagation();
        this.queryParams.sortedBy = event.detail;
        this.queryParams.sortDirection = this.queryParams?.sortDirection === 'asc' ? 'desc' : 'asc';

        this.dispatchEvent(
            new CustomEvent(
                'sort',
                {
                    bubbles: true,
                    composed: true,
                    detail: {
                        sortedBy: this.queryParams.sortedBy,
                        sortDirection: this.queryParams.sortDirection
                    }
                }
            )
        );
        this.resetSorting();

        let msgClassSortedBy = this.queryParams?.sortedBy;
        this.sortArrow[`${msgClassSortedBy}Lock`] = true;
        this.sortArrow[`${msgClassSortedBy}Show`] = true;
    }

    /**
     * @description Handler for the scroll event to check if more records need to be rendered.
     * @param event
     */
    async handleScroll(event) {
        if (this.loading) {
            return;
        }
        const target = event.target;

        const SCROLL_BUFFER = 5;

        if (target.scrollHeight - target.scrollTop <= target.clientHeight + SCROLL_BUFFER) {
            if (this.lazyLoading) {
                const newLimit = this.renderLimit + this.renderIncrement;

                if (this.renderLimit < this._records.length) {
                    this.notifyLoading(true);

                    await new Promise((resolve) => setTimeout(resolve, 0));

                    this.renderLimit = Math.min(newLimit, this._records.length);
                    this._renderedRecords = this._records.slice(0, this.renderLimit);

                    await new Promise((resolve) => setTimeout(resolve, 0));

                    this.notifyLoading(false);

                    if (this.renderLimit >= this._records.length) {
                        this.notifyLoading(false);
                    }
                } else {
                    this.notifyLoading(false);
                }
            } else {
                this.dispatchEvent(
                    new CustomEvent('loadmore', {
                        bubbles: false,
                        composed: false,
                    })
                );
            }
        }

        this.setOverflowStyles(target);
    }

    /**
     * @description: helper function to reset the table scroll to
     * the top
     */
    handleResetScroll() {
        const container = this.refs.table;
        if (container)
            container.scrollTop = 0;
    }

    /**
     * @description
     * @param event
     */
    handleCellChange(event) {
        let record = event.detail.draftValues[0];
        let existingDraftIndex = this.draftValues.findIndex(el => el.Id === record.Id);
        if (existingDraftIndex >= 0) {
            this.draftValues.splice(existingDraftIndex, 1, record);
        } else {
            this.draftValues = [...this.draftValues, record];
        }
    }

    /**
     * @description Dispatches a 'loading' custom event to notify parent components about
     * the current loading state and the number of rendered records. This can be used to
     * display or hide a loading spinner and provide contextual information about the
     * rendered data.
     *
     * @param {boolean} value - The loading state to notify. Typically `true` to indicate
     * loading has started and `false` to indicate loading has ended.
     *
     * @event
     * @property {boolean} detail.value - The current loading state.
     * @property {number} detail.renderedRecordCount - The count of records that have been
     * rendered at the time of the notification.
     */
    notifyLoading(value) {
        this.dispatchEvent(
            new CustomEvent(
                'loading',
                {
                    bubbles: true,
                    composed: true,
                    detail: {
                        value: value
                    }
                }
            )
        );
    }

    /**
     * Description: helper function to sort table data
     */
    sortTableData() {
        let parseData = JSON.parse(JSON.stringify(this._records));
        let keyValue = (a) => {
            return a[this.queryParams?.sortedBy];
        };

        let isReverse = this.queryParams?.sortDirection === 'asc' ? 1 : -1;

        parseData.sort((x, y) => {
            x = keyValue(x) ? keyValue(x) : '';
            y = keyValue(y) ? keyValue(y) : '';

            return isReverse * ((x > y) - (y > x));
        });

        return parseData;
    }

    setOverflowStyles(target) {
        this.showTableShadow = !(Math.abs(target.scrollLeft) >= target.scrollWidth - target.clientWidth);
    }


    setArrow(event) {
        let msgClassSortBy = event.detail;
        let lock = `${msgClassSortBy}Lock`;
        let show = `${msgClassSortBy}Show`;
        return !this.sortArrow[lock] ? this.sortArrow[show] = !this.sortArrow[show] : null;
    }

    resetSorting() {
        Object.keys(this.sortArrow).forEach(x => this.sortArrow[x] = false);
    }

    /**
     * Computes the CSS class for the empty state title based on whether an icon is present
     * @returns {string} CSS class for the empty state title
     */
    get emptyStateTitleClass() {
        return this.emptyStateIcon ? 'h3 mtm' : 'h3';
    }
}
