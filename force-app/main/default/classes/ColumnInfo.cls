/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 6/13/23.
 */
public inherited sharing class ColumnInfo {
    @AuraEnabled public String label;
    @AuraEnabled public String fieldName;
    @AuraEnabled public String type;
    @AuraEnabled public Boolean wrapText = true;
    @AuraEnabled public Boolean hideDefaultActions = true;
    @AuraEnabled public Boolean sortable = false;
    @AuraEnabled public Integer initialWidth = 80;
    @AuraEnabled public Map<String, Object> typeAttributes;

    public ColumnInfo (String columnLabel, String fieldName, String type, Integer initialWidth, Map<String, Object> typeAttributes) {
        this.label = columnLabel;
        this.fieldName = fieldName;
        this.type = type;
        this.initialWidth = initialWidth;
        this.typeAttributes = typeAttributes != null ? typeAttributes : new Map<String, Object>();
    }

    public ColumnInfo (String columnLabel, String fieldName, String type, Map<String, Object> typeAttributes) {
        this.label = columnLabel;
        this.fieldName = fieldName;
        this.type = type;
        this.typeAttributes = typeAttributes != null ? typeAttributes : new Map<String, Object>();
    }
}