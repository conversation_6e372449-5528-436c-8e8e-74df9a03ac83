/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 6/27/24.
 */

public without sharing class OpportunityDomain extends Domain {

    public OpportunityDomain() {
    }
    public OpportunityDomain(List<Opportunity> records) {
        super(records);
    }

    public override void doBeforeInsert() {
        defaults((List<Opportunity>) this.triggerRecords);
    }

    public override void doBeforeUpdate(Map<Id, SObject> oldRecordsMap) {
        defaults((List<Opportunity>) this.triggerRecords);
    }

    /**
     * Sets the Revenue Type
     * Sets the Household
     *
     * @param records The opportunities to process
     */
    public void defaults(List<Opportunity> records) {
        Map<Id, Account> parentAccountMap = new Map<Id, Account>();
        for (Opportunity oppy : records) {
            if (oppy.RevenueType__c == null || oppy.Household__c == null) {
                parentAccountMap.put(oppy.AccountId, null);
            }
        }
        parentAccountMap.remove(null);

        if (parentAccountMap.size() > 0) {
            parentAccountMap = new Map<Id, Account>([
                    SELECT Id, RevenueType__c, flmas__PrimaryHousehold__pc, RecordType.IsPersonType
                    FROM Account
                    WHERE Id IN :parentAccountMap.keySet()
            ]);
            for (Opportunity oppy : records) {
                Account a = parentAccountMap.get(oppy.AccountId);
                if (a != null) {
                    if (oppy.RevenueType__c == null) {
                        oppy.RevenueType__c = a?.RevenueType__c;
                    }
                    if (oppy.Household__c == null && a != null && a.RecordType?.IsPersonType == true && a.flmas__PrimaryHousehold__pc != null) {
                        oppy.Household__c = a.flmas__PrimaryHousehold__pc;
                    }
                }
            }
        }
    }
}