/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 5/21/25.
 */

public without sharing class TRG_ACR_Affiliation extends Domain {
    public TRG_ACR_Affiliation() {
        super();
    }

    public TRG_ACR_Affiliation(List<AccountContactRelation> triggerRecords) {
        super(triggerRecords);
    }

    public override void doAfterInsert() {
        syncToAffiliations(this.triggerRecords);
    }

    public static void syncToAffiliations(List<AccountContactRelation> records) {
        List<Affiliation__c> affiliationsToInsert = new List<Affiliation__c>();
        for (AccountContactRelation acr : records) {
            affiliationsToInsert.add(
                    new Affiliation__c(
                            Contact__c      = acr.ContactId,
                            Organization__c = acr.AccountId,
                            Role__c         = acr.Roles
                    )
            );
        }
        insert affiliationsToInsert;
    }
}