<?xml version="1.0" encoding="UTF-8"?>
<CustomMetadata xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
    <label>(Example) Gift Tx Des: Cash</label>
    <protected>false</protected>
    <values>
        <field>AccountLookupField__c</field>
        <value xsi:type="xsd:string">CashOrWOAccount__c</value>
    </values>
    <values>
        <field>DefaultAccountCodeFormula__c</field>
        <value xsi:type="xsd:string">&quot;1001-00000&quot;</value>
    </values>
    <values>
        <field>FormatValidation__c</field>
        <value xsi:type="xsd:string">(\d{4})-(\d{5})</value>
    </values>
    <values>
        <field>IsActive__c</field>
        <value xsi:type="xsd:boolean">true</value>
    </values>
    <values>
        <field>LedgerAccountObject__c</field>
        <value xsi:type="xsd:string">LedgerAccount__c</value>
    </values>
    <values>
        <field>ObjectWithAccountLookup__c</field>
        <value xsi:type="xsd:string">GiftTransactionDesignation</value>
    </values>
    <values>
        <field>Segment10Field__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment10ReplacementRules__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment10Type__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment10ValueFormula__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment1Field__c</field>
        <value xsi:type="xsd:string">GLAccount__c</value>
    </values>
    <values>
        <field>Segment1ReplacementRules__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment1Type__c</field>
        <value xsi:type="xsd:string">GL_Account</value>
    </values>
    <values>
        <field>Segment1ValueFormula__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment2Field__c</field>
        <value xsi:type="xsd:string">Project__c</value>
    </values>
    <values>
        <field>Segment2ReplacementRules__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment2Type__c</field>
        <value xsi:type="xsd:string">Project</value>
    </values>
    <values>
        <field>Segment2ValueFormula__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment3Field__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment3ReplacementRules__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment3Type__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment3ValueFormula__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment4Field__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment4ReplacementRules__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment4Type__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment4ValueFormula__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment5Field__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment5ReplacementRules__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment5Type__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment5ValueFormula__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment6Field__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment6ReplacementRules__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment6Type__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment6ValueFormula__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment7Field__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment7ReplacementRules__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment7Type__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment7ValueFormula__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment8Field__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment8ReplacementRules__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment8Type__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment8ValueFormula__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment9Field__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment9ReplacementRules__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment9Type__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>Segment9ValueFormula__c</field>
        <value xsi:nil="true"/>
    </values>
    <values>
        <field>SegmentSeparatorChar__c</field>
        <value xsi:type="xsd:string">-</value>
    </values>
</CustomMetadata>
