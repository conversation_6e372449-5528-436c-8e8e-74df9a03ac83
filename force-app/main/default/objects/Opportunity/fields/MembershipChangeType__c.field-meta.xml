<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>MembershipChangeType__c</fullName>
    <description>Whether this membership is new, an upgrade, a downgrade, or no change based on the prior membership from the same seller.
Do NOT change the picklist option values. Change ONLY the labels.</description>
    <inlineHelpText>Whether this membership is new, an upgrade, a downgrade, or no change.</inlineHelpText>
    <label>Membership Change Type</label>
    <required>false</required>
    <trackFeedHistory>false</trackFeedHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>NewMember</fullName>
                <default>false</default>
                <label>New Member</label>
            </value>
            <value>
                <fullName>Upgrade</fullName>
                <default>false</default>
                <label>Upgrade</label>
            </value>
            <value>
                <fullName>Downgrade</fullName>
                <default>false</default>
                <label>Downgrade</label>
            </value>
            <value>
                <fullName>NoChange</fullName>
                <default>false</default>
                <label>No Change</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
