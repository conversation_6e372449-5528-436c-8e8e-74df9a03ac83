<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>ReceivablesAccount__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <label>Receivables Account</label>
    <lookupFilter>
        <active>true</active>
        <errorMessage>The selected Ledger Account must be an A/R account</errorMessage>
        <filterItems>
            <field>LedgerAccount__c.GLAccount__r.AccountSubType__c</field>
            <operation>equals</operation>
            <value>A/R: Current, A/R: Long-Term</value>
        </filterItems>
        <isOptional>false</isOptional>
    </lookupFilter>
    <referenceTo>LedgerAccount__c</referenceTo>
    <relationshipLabel>Gift Transaction Designations (Receivables)</relationshipLabel>
    <relationshipName>GiftTransactionDesignations_AR</relationshipName>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <type>Lookup</type>
</CustomField>
