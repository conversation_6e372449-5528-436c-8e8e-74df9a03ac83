<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>PaymentWriteoffDate__c</fullName>
    <description>The field on the child payment object that indicates the date the payment was written-off.</description>
    <fieldManageability>SubscriberControlled</fieldManageability>
    <inlineHelpText>The field on the child payment object that indicates the date the payment was written-off.</inlineHelpText>
    <label>Payment: Write-off Date</label>
    <metadataRelationshipControllingField>SubledgerDefinition__mdt.PaymentObject__c</metadataRelationshipControllingField>
    <referenceTo>FieldDefinition</referenceTo>
    <relationshipLabel>SubledgerDefinitions_PayWriteOffDate</relationshipLabel>
    <relationshipName>SubledgerDefinitions_PayWriteOffDate</relationshipName>
    <required>false</required>
    <type>MetadataRelationship</type>
    <unique>false</unique>
</CustomField>
