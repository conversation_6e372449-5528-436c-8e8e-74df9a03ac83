/*
 * Copyright (c) 2024. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * Created by edwardblazer on 4/1/23.
 */
/**
 * The purpose of this class is to make it easier to use a Subledger Definition to
 *  - Query and find qualifying header and related records
 *  - Navigate the schema
 *  - Test with mocked definitions
 *
 * todo add subclasses for RevExpLineItem and Payment?
 */
public abstract with sharing class SubledgerDefinition {
    @TestVisible
    private static final List<SubledgerDefinition> ALL_DEFINITIONS                      = new List<SubledgerDefinition>();
    private static final Map<SObjectType,List<SubledgerDefinition>> ALL_DEFINITIONS_BY_OBJECT = new Map<SObjectType,List<SubledgerDefinition>>();
    private static final Map<SObjectType,List<SubledgerDefinition>> ALL_DEFINITIONS_BY_HEADER_OBJECT = new Map<SObjectType,List<SubledgerDefinition>>();
    /*private static final Map<SObjectType,List<SubledgerDefinition>> ALL_DEFINITIONS_BY_LINE_OBJECT = new Map<SObjectType,List<SubledgerDefinition>>();
    private static final Map<SObjectType,List<SubledgerDefinition>> ALL_DEFINITIONS_BY_PAY_OBJECT = new Map<SObjectType,List<SubledgerDefinition>>();
    private static final Map<SObjectType,List<SubledgerDefinition>> ALL_DEFINITIONS_BY_PAYLINE_OBJECT = new Map<SObjectType,List<SubledgerDefinition>>();*/

    //public final SubledgerDefinition__mdt record;


    public Id definitionId;
    public String definitionName;
    public DefinitionType type;
    public Boolean isEnabled;

    public String headerFilter;
    public SObjectType headerSObjectType;
    public SObjectField headerDateField;

    public SObjectField ledgerEntryHeaderLookup;
    public SObjectField ledgerEntryLineItemHeaderLookup;
    public SObjectField ledgerEntryLineItemSourceLookup;
    public Boolean groupLinesByAccount;


    static {
        for (SubledgerDefinition__mdt sd : [
                SELECT  Id,
                        Label,
                        DeveloperName,
                        IsEnabled__c,
                        DefinitionType__c,
                        HeaderFilter__c,
                        HeaderObject__r.QualifiedApiName,
                        HeaderDate__r.QualifiedApiName,
                        HeaderDate__r.EntityDefinition.QualifiedApiName,
                        LedgerEntryObject__r.QualifiedApiName,
                        LedgerEntryHeaderLookup__r.QualifiedApiName,
                        LedgerEntryHeaderLookup__r.RelationshipName,
                        LedgerEntryHeaderLookup__r.EntityDefinition.QualifiedApiName,
                        LedgerEntryLineItemObject__r.QualifiedApiName,
                        LedgerEntryLineItemHeaderLookup__r.QualifiedApiName,
                        LedgerEntryLineItemHeaderLookup__r.RelationshipName,
                        LedgerEntryLineItemHeaderLookup__r.EntityDefinition.QualifiedApiName,
                        LedgerEntryLineItemSourceLookup__r.QualifiedApiName,
                        LedgerEntryLineItemSourceLookup__r.RelationshipName,
                        LedgerEntryLineItemSourceLookup__r.EntityDefinition.QualifiedApiName,
                        GroupLedgersByAccount__c,
                        LineItemObject__r.QualifiedApiName,
                        LineItemType__c,
                        LineItemAmount__r.QualifiedApiName,
                        LineItemAmount__r.EntityDefinition.QualifiedApiName,
                        LineItemARorAPAcctLookup__r.QualifiedApiName,
                        LineItemARorAPAcctLookup__r.RelationshipName,
                        LineItemARorAPAcctLookup__r.EntityDefinition.QualifiedApiName,
                        LineItemRevOrExpAcctLookup__r.QualifiedApiName,
                        LineItemRevOrExpAcctLookup__r.RelationshipName,
                        LineItemRevOrExpAcctLookup__r.EntityDefinition.QualifiedApiName,
                        LineItemHeaderLookup__r.QualifiedApiName,
                        LineItemHeaderLookup__r.RelationshipName,
                        LineItemHeaderLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentObject__r.QualifiedApiName,
                        PaymentHeaderLookup__r.QualifiedApiName,
                        PaymentHeaderLookup__r.RelationshipName,
                        PaymentHeaderLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentPaymentAmount__r.QualifiedApiName,
                        PaymentPaymentAmount__r.EntityDefinition.QualifiedApiName,
                        PaymentDueDate__r.QualifiedApiName,
                        PaymentDueDate__r.EntityDefinition.QualifiedApiName,
                        PaymentPaidDate__r.QualifiedApiName,
                        PaymentPaidDate__r.EntityDefinition.QualifiedApiName,
                        PaymentWriteoffDate__r.QualifiedApiName,
                        PaymentWriteoffDate__r.EntityDefinition.QualifiedApiName,
                        PaymentCashOrWOAcctLookup__r.QualifiedApiName,
                        PaymentCashOrWOAcctLookup__r.RelationshipName,
                        PaymentCashOrWOAcctLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemObject__r.QualifiedApiName,
                        PaymentLineItemPaymentLookup__r.QualifiedApiName,
                        PaymentLineItemPaymentLookup__r.RelationshipName,
                        PaymentLineItemPaymentLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemLineItemLookup__r.QualifiedApiName,
                        PaymentLineItemLineItemLookup__r.RelationshipName,
                        PaymentLineItemLineItemLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemAmount__r.QualifiedApiName,
                        PaymentLineItemAmount__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemRevOrExpAcctLookup__r.QualifiedApiName,
                        PaymentLineItemRevOrExpAcctLookup__r.RelationshipName,
                        PaymentLineItemRevOrExpAcctLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemCashOrWOAcctLookup__r.QualifiedApiName,
                        PaymentLineItemCashOrWOAcctLookup__r.RelationshipName,
                        PaymentLineItemCashOrWOAcctLookup__r.EntityDefinition.QualifiedApiName,
                        PaymentLineItemARorAPAcctLookup__r.QualifiedApiName,
                        PaymentLineItemARorAPAcctLookup__r.RelationshipName,
                        PaymentLineItemARorAPAcctLookup__r.EntityDefinition.QualifiedApiName,

                        TransactionPathToHeader__c,
                        TransactionFilter__c,
                        TransactionDateFieldPath__c,
                        TransactionAmountFieldPath__c,
                        TransactionDebitAccountFieldPath__c,
                        TransactionCreditAccountFieldPath__c,
                        TransactionObject__r.QualifiedApiName,
                        TxDebitAccountFormat__r.Id, TxDebitAccountFormat__r.DeveloperName,
                        TxCreditAccountFormat__r.Id, TxCreditAccountFormat__r.DeveloperName
                FROM SubledgerDefinition__mdt
                WHERE IsEnabled__c = TRUE
        ]) {
            System.debug('SubledgerDefinition' + sd.DefinitionType__c);
            Type sdType = Type.forName('SubledgerDefinition' + sd.DefinitionType__c);

            if (sdType != null) {
                SubledgerDefinition def = (SubledgerDefinition) sdType.newInstance();
                def.init(sd);
                def.validate();
                ALL_DEFINITIONS.add(def);

                groupDefs(ALL_DEFINITIONS_BY_OBJECT, def.headerSObjectType, def);
                groupDefs(ALL_DEFINITIONS_BY_HEADER_OBJECT, def.headerSObjectType, def);

                for (SObjectType sot : def.getObjectTypes()) {
                    groupDefs(ALL_DEFINITIONS_BY_OBJECT, sot, def);
                }
            }
        }
    }



    public SubledgerDefinition() {}

    public virtual void init(SubledgerDefinition__mdt record){
        Assert.assert(record != null, 'A Subledger Definition record is required.');

        this.definitionId   = record.Id;
        this.definitionName = record.DeveloperName;
        this.isEnabled      = record.IsEnabled__c == true;
        this.type           = DefinitionType.valueOf(record.DefinitionType__c);

        this.headerFilter       = record.HeaderFilter__c;
        this.headerSObjectType  = SObjectUtils.getSObjectType(record.HeaderObject__r);
        this.headerDateField    = SObjectUtils.getSObjectField(record.HeaderDate__r);

        this.ledgerEntryHeaderLookup            = SObjectUtils.getSObjectField(record.LedgerEntryHeaderLookup__r);
        this.ledgerEntryLineItemHeaderLookup    = SObjectUtils.getSObjectField(record.LedgerEntryLineItemHeaderLookup__r);
        this.ledgerEntryLineItemSourceLookup    = SObjectUtils.getSObjectField(record.LedgerEntryLineItemSourceLookup__r);
        this.groupLinesByAccount                = record.GroupLedgersByAccount__c == true;
    }

    public virtual void validate() {
        Assert.paramRequired(this.type, 'Definition Type');
        Assert.paramRequired(this.headerSObjectType, 'HeaderObject__r');
        Assert.paramRequired(this.headerDateField, 'HeaderDate__r');
        Assert.paramRequired(this.ledgerEntryHeaderLookup, 'LedgerEntryHeaderLookup__r');
        Assert.paramRequired(this.ledgerEntryLineItemHeaderLookup, 'LedgerEntryLineItemHeaderLookup__r');
    }

    /**
     * @return A SOQL query to get all of the source data for specified header records
     */
    public abstract String getSourceRecordsQuery();

    public abstract String getGroupFieldPath();

    public abstract SObjectType getSourceRecordsObjectType();

    public abstract Set<SObjectType> getObjectTypes();

    public abstract String getHeaderPath();

    public abstract HeaderProcessor getProcessor(
            SObject header,
            List<SObject> sourceRecords,
            List<LedgerEntry__c> existingLedgerEntries);

    /**
     * @param newRecords the new state of records to qualify
     * @param oldRecords the prior state of records to qualify
     *
     * @return a set of the parent ids that are qualified to have ledgers calculated
     */
    public abstract Set<Id> qualifyRecords(List<SObject> newRecords, Map<Id,SObject> oldRecords);

    /**
     * @param recordIds the record ids to validate
     * @return A SOQL query that will find all qualifying header records. Useful for batch job.
     */
    public Database.QueryLocator getFindQualifyingRecordsQueryLocator(Set<Id> recordIds) {
        DB dbI = DB.init().setNamespace('flmas');
        Selector sel = new Selector(this.headerSObjectType, dbI);

        sel.addField('Id');
        sel.addField(this.headerDateField);
        if (!String.isBlank(this.headerFilter)) {
            sel.addFilter('(' + this.headerFilter + ')');
        }
        // #150 fix something like: sel.addFilter(Selector.LogicOperator.OP_OR, '(SELECT {ledgerEntryHeaderLookup} FROM LedgerEntry__c WHERE SubledgerDefinitionId__c = {defId})');
        // update this doesn't work because SOQL doesn't support semi-joins with OR
        if (recordIds != null && recordIds.size() > 0) {
            sel.addFilter('Id', Selector.Comparator.IS_IN, recordIds);
        }

        sel.addOrderBy(this.headerDateField, true, true);

        return sel.runQueryLocator();
    }

    public Map<Object,List<SObject>> getSourceRecords(Set<Id> headerIds) {
        return (Map<Object,List<SObject>>)SObjectUtils.resultsByFieldPath(
                DB.init(false,false,false)
                        .read(this.getSourceRecordsQuery())
                        .setParam('headerIds',headerIds)
                        .go()
                , this.getGroupFieldPath()
        );
    }

    public Map<Object,List<SObject>> getLedgerRecords(Set<Id> headerIds) {
        String lehl = String.valueOf(this.ledgerEntryHeaderLookup);
        String lelihl = String.valueOf(this.ledgerEntryLineItemHeaderLookup);

        String soql =   'SELECT Id, Status__c, PostDate__c, TransactionDate__c, {headerRef}, SubledgerDefinitionId__c, \n' +
                        '(\n' +
                        '        SELECT Id, Amount__c, Type__c, {lineHeaderRef}, SourceRecordId__c,\n' +
                        '               LedgerAccount__r.Id, LedgerAccount__r.Name, LedgerAccount__r.GLAccount__r.Id, LedgerAccount__r.GLAccount__r.AccountSubType__c,\n' +
                        '               LedgerEntry__r.PostDate__c, LedgerEntry__r.TransactionDate__c, LedgerEntry__r.Status__c\n'+
                        '          FROM LineItems__r\n' +
                        '         ORDER BY LedgerAccount__r.GLAccount__r.GLCode__c ASC\n'+
                        ')\n' +
                        '  FROM LedgerEntry__c\n' +
                        ' WHERE {headerRef} IN :headerIds AND SubledgerDefinitionId__c = :defId';
        soql    = StringUtils.format(soql)
                .set('{headerRef}', lehl)
                .set('{lineHeaderRef}', lelihl)
                .toString();
        return (Map<Object,List<SObject>>)SObjectUtils.resultsByFieldPath(
                DB.init(false,false,false)
                        .read(soql)
                        .setParam('headerIds',headerIds)
                        .setParam('defId', this.definitionId) // Get the ledgers that are related to this ledger definition
                        .go()
                , lehl
        );
    }

    /**
     * Used by LedgerEntryService.cascadeDelete to find the HeaderIds for the specified sourceRecords
     * so that the Ledgers related to the headers can be deleted.
     *
     * The source records are grouped by the header Id so that if there are any errors deleting the ledgers
     * we can apply the error to the source record(s)
     *
     * @param sourceRecords The source records (oppy lines, payments, payment allocations, etc)
     *
     * @return A Map of source records grouped by their parent header id
     *
    public Map<Id,List<SObject>> getRelatedHeaderIds(List<SObject> sourceRecords) {
        SObjectType sot = sourceRecords?.getSObjectType();

        Map<Id,List<SObject>> headerIds = new Map<Id,List<SObject>>();

        /*SObjectField lookupField;
        // Determine which field has the lookup to the header
        if (sot == this.headerSObjectType) {
            lookupField = this.headerSObjectType.getDescribe().fields.getMap().get('Id');
        } else if (sot == this.lineItemSObjectType) {
            lookupField = this.lineItemHeaderLookup;
        } else if (sot == this.paymentSObjectType) {
            lookupField = this.paymentHeaderLookup;
        }

        if (lookupField != null) {
            for (SObject rec : sourceRecords) {
                Id lookupId = (Id)rec.get(lookupField);
                List<SObject> recs = headerIds.get(lookupId);
                if (recs == null) {
                    recs = new List<SObject>();
                    headerIds.put(lookupId, recs);
                }
                recs.add(rec);
            }
        } else* / if (sot == this.getSourceRecordsObjectType()) {
            // The PaymentLineItem allocating object is being used, and it does not have a header lookup.
            // Need to derive it based on a query.
            String headerPath = this.getHeaderPath();//this.payLineItemLineItemRef + '.' + this.lineItemHeaderLookup;
            String soql = StringUtils.format('SELECT Id, {headerPath} FROM {obj} WHERE Id IN :sourceRecords')
                    .set('{headerPath}',headerPath)
                    .set('{obj}',String.valueOf(sot))
                    .toString();
            System.debug(LoggingLevel.DEBUG, 'SOQL: ' + soql);
            Map<Id,SObject> sourceRecordsMap = new Map<Id,SObject>(sourceRecords);
            for (SObject srcFull : Database.query(soql)) {
                Id lookupId = (Id)SObjectUtils.getValueForPath(srcFull, headerPath);
                List<SObject> recs = headerIds.get(lookupId);
                if (recs == null) {
                    recs = new List<SObject>();
                    headerIds.put(lookupId, recs);
                }
                recs.add(sourceRecordsMap.get(srcFull.Id));
            }
        }

        return headerIds;
    }*/

    public Boolean isEnabled() {
        return this.isEnabled;
    }

    public DefinitionType getType() {
        return this.type;
    }

    public SObjectField getFieldHeaderDate() {
        return this.headerDateField;//SObjectUtils.getSObjectField(this.record.HeaderDate__r);
    }

    public SObjectField getFieldLedgerEntryHeaderLookup() {
        return this.ledgerEntryHeaderLookup;//SObjectUtils.getSObjectField(this.record.LedgerEntryHeaderLookup__r);
    }

    public SObjectField getFieldLedgerEntryLineItemHeaderLookup() {
        return this.ledgerEntryLineItemHeaderLookup;//SObjectUtils.getSObjectField(this.record.LedgerEntryLineItemHeaderLookup__r);
    }

    public enum DefinitionType {
        Apportioned,
        Tx
    }

    public static List<SubledgerDefinition> getAllDefinitions() {
        return ALL_DEFINITIONS;
    }

    public static List<SubledgerDefinition> getAllDefinitions(Set<SObjectType> headerObjects) {
        Map<Id,SubledgerDefinition> defs = new Map<Id,SubledgerDefinition>();
        for (SObjectType sot : headerObjects) {
            if (ALL_DEFINITIONS_BY_OBJECT.containsKey(sot)) {
                for (SubledgerDefinition sd : ALL_DEFINITIONS_BY_OBJECT.get(sot)) {
                    defs.put(sd.definitionId, sd);
                }
            }
        }

        return defs.values();
    }

    public static List<SubledgerDefinition> getDefinition(String developerName) {
        List<SubledgerDefinition> defs = new List<SubledgerDefinition>();
        for (SubledgerDefinition sd : ALL_DEFINITIONS) {
            if (sd.definitionName.equalsIgnoreCase(developerName)) {
                defs.add(sd);
            }
        }
        return defs;
    }


    private static void groupDefs(Map<SObjectType,List<SubledgerDefinition>> subDefMap, SObjectType sot, SubledgerDefinition def) {
        List<SubledgerDefinition> defs = subDefMap.get(sot);
        if (defs == null) {
            defs = new List<SubledgerDefinition>();
            subDefMap.put(sot, defs);
        }
        defs.add(def);
    }
}