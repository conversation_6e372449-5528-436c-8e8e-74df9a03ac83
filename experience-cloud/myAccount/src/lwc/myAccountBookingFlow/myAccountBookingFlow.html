<!--
  ~ Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<!-- My Account Booking Flow -->
<template>
    <h1 class="h1 color--brand mbl">Book a field trip</h1>

    <div class="booking-flow-container">
        <template if:true={hasActiveCart}>
            <div class="cart-timer sticky background--white border-bottom prs pls ptxs pbxs">
                <div class="flex flex-gap-sp align-items-center justify-content-end">
                    <img src={clockIcon} alt="Clock" width="16" height="16" />
                    <p class="p p--small">
                        <span>Cart expires in: </span>
                        <span class={timerClass}>{cartTimeRemaining}</span>
                    </p>
                </div>
            </div>
        </template>

        <div class="path-container">
            <div class="path">
                <template for:each={steps} for:item="step">
                    <div key={step.name}
                         class={step.classes}
                         onclick={handleStepClick}
                         data-step={step.number}>
                        <div class="step-circle">
                            {step.number}
                        </div>
                        <div class="step-label">
                            {step.label}
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <div class="flow-content">
            <template if:true={shouldShowErrorMessage}>
                <div class="error-message background--yellow border border-radius--medium pts prs pbs pls mbm">
                    <p class="p color--error font-weight--bold">{bookingState.error}</p>
                </div>
            </template>

            <div class="flow-step-container">
                <c-my-account-flow-display
                        lwc:ref="flow"
                        account-id={accountId}
                        schools={schools}
                        current-step={currentStep}
                        booking-data={bookingData}
                        booking-state={bookingState}
                        onrefreshschools={handleRefreshSchools}
                        onflowdatachange={handleFlowDataChange}
                        onsurveysubmit={submitSurveyResponses}
                        oncompletebooking={completeBooking}>
                </c-my-account-flow-display>
            </div>

            <div class="button-container flex space-between mtl">
                <flmas-my-account-button
                        label="Back"
                        variant="neutral"
                        loading={loading}
                        onclick={handlePrevious}
                        disabled={isPreviousDisabled}>
                </flmas-my-account-button>

                <flmas-my-account-button
                        label={nextButtonLabel}
                        variant="brand"
                        loading={loading}
                        onclick={handleNext}
                        disabled={isNextDisabled}>
                </flmas-my-account-button>
            </div>
        </div>
    </div>
</template>