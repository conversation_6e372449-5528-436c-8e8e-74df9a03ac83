/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

import {LightningElement, api, track} from 'lwc';
import virtualProgramText from '@salesforce/label/c.MyAccount_VirtualProgramText';
import onsiteProgramText from '@salesforce/label/c.MyAccount_OnsiteProgramText';
import termsAndConditionsText from '@salesforce/label/c.MyAccount_TermsAndConditionsText';
import {ShowToastEvent} from "flmas/showToastEvent";

export default class MyAccountCheckout extends LightningElement {
    @api bookingData;
    @api bookingState;

    @track sendEmail = true;
    @track termsAccepted = false;
    @track isSubmitting = false;

    labels = {
        virtualProgramText,
        onsiteProgramText,
        termsAndConditionsText
    };

    handleSendEmailChange(event) {
        this.sendEmail = event.target.checked;
    }

    handleTermsChange(event) {
        this.termsAccepted = event.target.checked;
    }

    /**
     * Validates that the terms and conditions have been accepted
     * @returns {Boolean} True if terms are accepted, false otherwise
     */
    @api
    validate() {
        if (!this.termsAccepted) {
            this.dispatchEvent(new ShowToastEvent({
                label: 'Terms Required',
                message: 'Please accept the terms and conditions to proceed.',
                variant: 'error'
            }));
            return false;
        }
        return true;
    }

    handleSubmit() {
        if (!this.validate()) {
            return;
        }

        this.isSubmitting = true;

        this.dispatchEvent(new CustomEvent('completebooking', {
            detail: {
                sendEmail: this.sendEmail
            }
        }));
    }

    handleBack() {
        this.dispatchEvent(new CustomEvent('back'));
    }

    get isDisabled() {
        return this.isSubmitting || this.bookingState?.status === 'error';
    }

    get programName() {
        return this.bookingData?.program?.name || 'Unknown Program';
    }

    get sessionDate() {
        return this.bookingData?.session?.formattedStartDate || 'Pending';
    }

    get sessionTime() {
        return this.bookingData?.session?.formattedStartTime || 'Pending';
    }

    get schoolName() {
        const school = this.bookingData?.school;
        return school ? `${school.name}${school.city ? `, ${school.city}` : ''}` : 'Unknown School';
    }

    get numStudents() {
        return this.bookingData?.filters?.students || 'Pending';
    }

    get gradeLevel() {
        return this.bookingData?.filters?.gradeLevel || 'Pending';
    }

    /**
     * Determines if the program is virtual based on includeOnsiteAdmission flag
     * @returns {Boolean} True if the program is virtual, false otherwise
     */
    get isVirtual() {
        // If includeOnsiteAdmission is false, it means the program is virtual
        return this.bookingData?.program?.includeOnsiteAdmission === false;
    }

    /**
     * Gets the appropriate program type text based on whether it's virtual or not
     * @returns {String} The program type text from custom label
     */
    get programTypeText() {
        return this.isVirtual ? this.labels.virtualProgramText : this.labels.onsiteProgramText;
    }

    /**
     * Gets the program image URL from the booking data
     * @returns {String} The program image URL or undefined
     */
    get programImage() {
        return this.bookingData?.program?.imageProfile;
    }

    /**
     * Gets the program location from the booking data
     * @returns {String} The program location or 'Unknown'
     */
    get programLocation() {
        return this.bookingData?.program?.location || 'Unknown';
    }
}
