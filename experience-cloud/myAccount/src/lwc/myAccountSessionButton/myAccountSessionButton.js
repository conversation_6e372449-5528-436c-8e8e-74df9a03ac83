/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

import { LightningElement, api } from 'lwc';

export default class MyAccountSessionButton extends LightningElement {
    @api session;
    @api bookingData;
    @api bookingState;

    get buttonVariant() {
        return this.session.soldOut ? 'neutral' : 'brand';
    }

    get buttonLabel() {
        return this.session.formattedStartTime + (this.session.soldOut ? ' (Sold Out)' : '');
    }
    
    get isLoading() {
        return this.isSelected && this.bookingState.status === 'processing';
    }

    get isSelected() {
        return this.bookingData?.session?.id === this.session.id;
    }

    get isDisabled() {
        return this.session.soldOut ||
               (this.bookingState.status === 'processing' && !this.isSelected) ||
                (this.bookingState.status !== 'processing' && this.isSelected);
    }

    handleClick(event) {
        event.stopPropagation();
        // Only allow clicks if not disabled and not already loading
        if (!this.isDisabled && !this.isLoading) {
        this.dispatchEvent(new CustomEvent('sessionselect', {
            detail: {
                sessionId: this.session.id
            }
        }));
    }
}
}