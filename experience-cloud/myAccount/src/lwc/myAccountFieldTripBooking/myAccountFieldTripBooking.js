/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * myAccountFieldTripBooking
 * @description:
 * @author: <PERSON><PERSON>
 * @date: 4/16/25
 */
import { track, api, wire } from 'lwc';
import MyAccountPage from "flmas/myAccountPage";
import hasValidSchoolAffiliation from '@salesforce/apex/flmas.MyAccountSchoolService.hasValidSchoolAffiliation';

export default class MyAccountFieldTripBooking extends MyAccountPage {

    get accountId() {
        return this.currentPage?.accountId;
    }

    handleBookingComplete() {

    }

    @wire(hasValidSchoolAffiliation, {
        sessionToken: '$sessionToken',
        accountId: '$accountId'
    })
    wiredAffiliationCheck({ error, data }) {
        if (data === false) {
            this.handleUnauthorizedAccess();
        } else if (error) {
            console.error('Error checking affiliation:', error);
        }
    }

    handleUnauthorizedAccess() {

    }
}