/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@import 'flmas/myAccountGlobalStyles';

.path-container {
    width: 100%;
    overflow-x: auto;
}

.path {
    list-style: none;
    padding: 0;
    margin: 0;
    position: relative;
    gap: var(--rp);
}

.path::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
    z-index: 1;
}

.step {
    flex: 1;
    position: relative;
    z-index: 2;
    background: var(--background-color-white);
    cursor: pointer;
    max-width: 150px;
}

.step-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem;
}

.step-number {
    width: 2rem;
    height: 2rem;
    background: var(--border-color);
    color: var(--text-color-inverted);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.step.active .step-number {
    background: var(--button-brand-color-background);
}

.step.completed .step-number {
    background: var(--button-brand-color-background-hover);
}

.step.upcoming {
    cursor: not-allowed;
}

.step-name {
    font-size: 0.875rem;
    color: var(--text-color-light);
}

.step.active .p {
    color: var(--text-color);
    font-weight: var(--bold);
}

.step.upcoming .p {
    color: var(--text-color-light);
}

.flow-step {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.flow-step-content {
    flex: 1;
    overflow-y: auto;
}
