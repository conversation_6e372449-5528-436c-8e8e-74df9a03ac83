/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

@import 'flmas/myAccountGlobalStyles';

.selected-program-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    animation: slideUp 0.3s ease-out;
}

.selected-program-panel {
    background-color: var(--background-color-white);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.selected-program-card {
    background-color: var(--background-color-white);
    border-top: 3px solid var(--primary-color);
    max-width: 1200px;
    margin: 0 auto;
}

.selected-session {
    background-color: var(--background-color-light);
    border-radius: var(--border-radius-small);
    padding: var(--xsp) var(--sp);
    border: 1px solid var(--border-color);
}

.program-image-container-small {
    width: 60px;
    height: 60px;
    min-width: 60px;
    overflow: hidden;
    border-radius: var(--border-radius-small);
    margin-right: var(--sp);
}

.program-image-small {
    width: 100%;
    height: 100%;
    object-fit: cover;
}