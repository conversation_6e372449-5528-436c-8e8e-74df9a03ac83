<!--
  ~ Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
  ~
  ~ The code below is part of the Foglight Arts & Culture package and is not
  ~ to be redistributed without express written consent by Foglight.
  -->

<template>
    <div class="selected-program-container">
        <div class="selected-program-panel">
            <div class="selected-program-card border-top ptm pbm prm plm">
                <div class="flex space-between align-items-flex-start mbs">
                    <div class="flex flex-gap-rp align-items-flex-start">
                        <template if:true={program.imageProfile}>
                            <div class="program-image-container-small">
                                <img src={program.imageProfile} alt={program.name} class="program-image-small"/>
                            </div>
                        </template>
                        <div>
                            <h3 class="h3">Selected: {program.name}</h3>
                            <h4 class="h4">{program.templateName}</h4>
                        </div>
                    </div>
                    <div class="flex flex-gap-rp">
                        <template if:true={session}>
                            <flmas-my-account-button
                                label="Remove"
                                variant="destructive"
                                loading={isRemovingTickets}
                                onclick={handleClearSelection}>
                            </flmas-my-account-button>

                            <flmas-my-account-button
                                label={reserveButtonLabel}
                                variant="brand"
                                loading={isProcessing}
                                disabled={isContinueDisabled}
                                onclick={handleContinue}>
                            </flmas-my-account-button>
                        </template>
                    </div>
                </div>
                <div class="selected-program-details">
                    <div class="flex space-between mbs">
                        <div class="detail-item">
                            <span class="p">Grade Band:</span>
                            <span class="p p--black"> {program.gradeLevel}</span>
                        </div>
                        <div class="detail-item">
                            <span class="p">Capacity:</span>
                            <span class="p p--black"> {program.capacity}</span>
                        </div>
                    </div>
                    <template if:true={session}>
                        <div class="selected-session mts">
                            <span class="p">Selected Time:</span>
                            <span class="p p--black"> {session.formattedStartTime}</span>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>