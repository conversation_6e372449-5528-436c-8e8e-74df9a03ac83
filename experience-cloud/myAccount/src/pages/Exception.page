<apex:page showHeader="false" title="{!$Label.site.error2}" cache="false">
  <apex:composition template="{!$Site.Template}">
    <apex:define name="body">
      <center>
        <apex:panelGrid bgcolor="white" columns="1" style="align: center;">
          <br/>
          <br/>
          <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="1" styleClass="topPanelContainer">
            <br/>
            <apex:outputPanel layout="block" styleClass="topPanel">
              <apex:panelGrid width="758" cellpadding="0" cellspacing="0" bgcolor="white" columns="3">
                <apex:image url="{!URLFOR($Resource.SiteSamples, 'img/clock.png')}"/>
                <apex:image url="{!URLFOR($Resource.SiteSamples, 'img/warning.png')}"/>
                <apex:panelGroup >
                  <apex:outputText styleClass="title" value="{!$Label.site.error}">
                   <apex:param value="{!$Site.ErrorMessage}"/>
                   <!-- this parameter needs to be italic in the site.error label -->
                  </apex:outputText>
                  <br/>
                  <br/>
                </apex:panelGroup>
              </apex:panelGrid>
             </apex:outputPanel>
            <c:SitePoweredBy />
          </apex:panelGrid>
          <br/>
          <apex:messages />
          <br/>
        </apex:panelGrid>
      </center>
    </apex:define>
  </apex:composition>
</apex:page>