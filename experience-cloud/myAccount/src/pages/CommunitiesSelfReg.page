<apex:page id="communitiesSelfRegPage" showHeader="true" controller="CommunitiesSelfRegController" title="{!$Label.site.user_registration}">
     <apex:define name="body">  
      <center>
<apex:form id="theForm" forceSSL="true">
                    <apex:pageMessages id="error"/>
                    <apex:panelGrid columns="2" style="margin-top:1em;">
                      <apex:outputLabel value="First Name" for="firstName"/>
                      <apex:inputText required="true" id="firstName" value="{!firstName}" label="First Name"/>
                      <apex:outputLabel value="Last Name" for="lastName"/>
                      <apex:inputText required="true" id="lastName" value="{!lastName}" label="Last Name"/>
                      <apex:outputLabel value="{!$Label.site.community_nickname}" for="communityNickname"/>
                      <apex:inputText required="true" id="communityNickname" value="{!communityNickname}" label="{!$Label.site.community_nickname}"/>
                      <apex:outputLabel value="{!$Label.site.email}" for="email"/>
                      <apex:inputText required="true" id="email" value="{!email}" label="{!$Label.site.email}"/>
                      <apex:outputLabel value="{!$Label.site.password}" for="password"/>
                      <apex:inputSecret id="password" value="{!password}"/>
                      <apex:outputLabel value="{!$Label.site.confirm_password}" for="confirmPassword"/>
                      <apex:inputSecret id="confirmPassword" value="{!confirmPassword}"/>
                      <apex:outputText value=""/>
                      <apex:commandButton action="{!registerUser}" value="{!$Label.site.submit}" id="submit"/>
                    </apex:panelGrid> 
                  <br/>
</apex:form>
     </center>
      <br/>
    </apex:define>

</apex:page>