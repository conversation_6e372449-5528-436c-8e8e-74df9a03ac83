/*
 * Copyright (c) 2025. Foglight Solutions, Inc. All Rights Reserved.
 *
 * The code below is part of the Foglight Arts & Culture package and is not
 * to be redistributed without express written consent by Foglight.
 */

/**
 * API class for making authenticated staff API calls to Ticketure
 * Handles session cookie management and automatic re-authentication
 */
global inherited sharing class MyAccountTicketureStaffAPI implements Callable {
	private static final String DEFAULT_CONFIG_NAME = 'Default';

	private String cmsEndpoint;
	private String username;
	private String password;

	private String sessionCookie;
	private Datetime sessionExpires;

	global String lastBodyReceived = '';
	public String lastRequestSubmitted = '';
	public String lastEndpoint = '';

	public List<RequestResponse> requestsResponses = new List<RequestResponse>();

	/**
	 * Default constructor required by Callable interface
	 */
	global MyAccountTicketureStaffAPI() {
	}

	/**
	 * Private constructor used by the setupAPI methods
	 */
	private MyAccountTicketureStaffAPI(Boolean isInSetupPage) {
		MyAccountTicketureConfig__mdt config = [
				SELECT StaffUsername__c, StaffPassword__c
				FROM MyAccountTicketureConfig__mdt
				WHERE DeveloperName = :DEFAULT_CONFIG_NAME
				LIMIT 1
		];

		this.cmsEndpoint = tixtrack.TixSettings.getSettings().cmsUrl;
		this.username = config.StaffUsername__c;
		this.password = config.StaffPassword__c;

		if (String.isBlank(this.cmsEndpoint) && isInSetupPage != true) {
			throw new TicketureStaffAPIException('CMS Endpoint must be configured in the TixTrack connector settings.');
		}

		if (isInSetupPage != true) {
			authenticate();
		}
	}

	/**
	 * Authenticate with the Ticketure API and get a session cookie
	 */
	private void authenticate() {
		if (String.isBlank(this.username) || String.isBlank(this.password)) {
			throw new TicketureStaffAPIException('Staff username and password must be configured in the Custom Metadata.');
		}

		Map<String, Object> requestBody = new Map<String, Object>{
				'method' => 'ticketure_staff',
				'username' => this.username,
				'password' => this.password
		};

		// Suppress authentication retry to avoid infinite loops
		this.suppressAuthenticationRetry = true;
		HttpResponse response = doCallout(
				JSON.serialize(requestBody),
				this.cmsEndpoint + '/api/login',
				'POST',
				null
		);
		this.suppressAuthenticationRetry = false;

		if (response.getStatusCode() == 204) {
			for (String headerKey : response.getHeaderKeys()) {
				if (headerKey.equalsIgnoreCase('Set-Cookie')) {
					String cookieHeader = response.getHeader(headerKey);
					if (cookieHeader.contains('session_mba=')) {
						this.sessionCookie = extractCookieValue(cookieHeader, 'session_mba');
						System.debug('Found session cookie: ' + this.sessionCookie);
					}
					if (cookieHeader.contains('session_expires=')) {
						String expiresValue = extractCookieValue(cookieHeader, 'session_expires');
						if (String.isNotBlank(expiresValue)) {
							// Convert the expiration value (in seconds) to a Datetime
							// The value represents seconds from now, not an absolute timestamp
							Long expiresInSeconds = Long.valueOf(expiresValue);
							this.sessionExpires = System.now().addSeconds(Integer.valueOf(expiresInSeconds));
							System.debug('Found session expiration: ' + this.sessionExpires);
						}
					}
				}
			}

			if (String.isBlank(this.sessionCookie)) {
				throw new TicketureStaffAPIException('Failed to extract session cookie from authentication response.');
			}
		} else {
			throw new TicketureStaffAPIException('Authentication failed: ' + response.getStatusCode() + ' ' + response.getStatus() + ' - ' + response.getBody());
		}
	}

	/**
	 * Extract a specific cookie value from a Set-Cookie header
	 */
	private String extractCookieValue(String cookieHeader, String cookieName) {
		System.Pattern p = System.Pattern.compile(cookieName + '=([^;]+)');
		System.Matcher m = p.matcher(cookieHeader);
		if (m.find()) {
			return m.group(1);
		}
		return null;
	}

	/**
	 * Flag to prevent authentication retry loops
	 */
	private Boolean suppressAuthenticationRetry = false;

	/**
	 * Setup API with default parameters
	 */
	global static MyAccountTicketureStaffAPI setupAPI() {
		return setupAPI(false);
	}

	/**
	 * Setup API with option for setup page flag
	 */
	global static MyAccountTicketureStaffAPI setupAPI(Boolean isInSetupPage) {
		return new MyAccountTicketureStaffAPI(isInSetupPage);
	}

	/**
	 * Callable interface implementation
	 */
	global Object call(String action, Map<String, Object> args) {
		switch on action {
			when 'getPath' {
				return getPath((String) args.get('path'), (Map<String, Object>) args.get('args'));
			}
			when 'doCallout' {
				return doCallout(
						String.valueOf(args.get('theBody')),
						String.valueOf(args.get('endpoint')),
						String.valueOf(args.get('httpVerb')),
						(Map<String, String>) (args.get('headers'))
				);
			}
			when else {
				throw new TicketureStaffAPIException('Unsupported action: ' + action);
			}
		}
	}

	/**
	 * Make an HTTP callout with automatic session management
	 */
	public HttpResponse doCallout(String theBody, String endpoint, String httpVerb, Map<String, String> headers) {
		RequestResponse rr = new RequestResponse();
		this.requestsResponses.add(rr);

		if (headers == null) {
			headers = new Map<String, String>();
		}

		// Ensure we have a valid session before proceeding
		ensureValidSession();

		if (String.isNotBlank(this.sessionCookie) && this.sessionExpires != null) {
			String cookieValue = buildSessionCookieHeader();
			headers.put('Cookie', cookieValue);
		}

		HttpRequest theRequest = new HttpRequest();
		rr.request = theRequest;

		theRequest.setMethod(httpVerb);
		if (headers != null) {
			for (String headerKey : headers.keySet()) {
				String headerValue = headers.get(headerKey);
				theRequest.setHeader(headerKey, headerValue);
			}
		}

		theRequest.setHeader('Content-Type', 'application/json');
		theRequest.setHeader('Accept', 'application/json');
		theRequest.setEndpoint(endpoint);

		lastRequestSubmitted = httpVerb + ' ' + endpoint;
		lastRequestSubmitted += '\nHEADERS:\n';
		Map<String, String> headsClean = headers.clone();
		lastRequestSubmitted += JSON.serializePretty(headsClean);
		lastRequestSubmitted += '\nBODY:\n';
		lastRequestSubmitted += theBody;

		rr.requestHeaders = headsClean;
		lastEndpoint = endpoint;

		if (theBody != null) {
			theRequest.setBody(theBody);
		}

		System.debug('fullEndpoint: ' + endpoint);
		System.debug('httpVerb: ' + httpVerb);
		System.debug('Request: ' + theBody);

		HttpResponse theResponse = new Http().send(theRequest);
		rr.response = theResponse;

		System.debug('Got first response: ' + theResponse.getStatusCode());

		// If we get an unauthorized response and we're not already in an authentication retry
		if (theResponse.getStatusCode() == 401 && !this.suppressAuthenticationRetry) {
			// Get a new session
			authenticate();

			// Update the cookie header with the new session
			if (String.isNotBlank(this.sessionCookie) && this.sessionExpires != null) {
				theRequest.setHeader('Cookie', buildSessionCookieHeader());
			}

			// Retry the request
			theResponse = new Http().send(theRequest);
			rr.response = theResponse;

			System.debug('Got retry response: ' + theResponse.getStatusCode());
		}

		// Update session cookie if provided in response
		updateSessionFromResponse(theResponse);

		return theResponse;
	}

	/**
	 * Ensures we have a valid session before making a request
	 */
	private void ensureValidSession() {
		if (this.suppressAuthenticationRetry) {
			return; // Skip validation during authentication to prevent loops
		}

		Boolean needsAuthentication = false;

		// Check if we have both cookie and expiration
		if (String.isBlank(this.sessionCookie) || this.sessionExpires == null) {
			needsAuthentication = true;
		} else {
			Long remainingSeconds = (this.sessionExpires.getTime() - System.now().getTime()) / 1000;
			if (remainingSeconds <= 0) {
				needsAuthentication = true;
			}
		}

		if (needsAuthentication) {
			authenticate();

			if (String.isBlank(this.sessionCookie) || this.sessionExpires == null) {
				throw new TicketureStaffAPIException('Failed to obtain a valid session after authentication.');
			}
		}
	}

	/**
	 * Builds the session cookie header string
	 */
	private String buildSessionCookieHeader() {
		String cookieValue = 'session_mba=' + this.sessionCookie;

		if (this.sessionExpires != null) {
			Long remainingSeconds = (this.sessionExpires.getTime() - System.now().getTime()) / 1000;
			if (remainingSeconds > 0) {
				cookieValue += '; session_expires=' + remainingSeconds;
			}
		}

		return cookieValue;
	}

	/**
	 * Updates session information from response headers
	 */
	private void updateSessionFromResponse(HttpResponse response) {
		for (String headerKey : response.getHeaderKeys()) {
			if (headerKey.equalsIgnoreCase('Set-Cookie')) {
				String cookieHeader = response.getHeader(headerKey);

				if (cookieHeader.contains('session_mba=')) {
					String newCookie = extractCookieValue(cookieHeader, 'session_mba');
					if (String.isNotBlank(newCookie)) {
						this.sessionCookie = newCookie;
					}
				}

				if (cookieHeader.contains('session_expires=')) {
					String expiresValue = extractCookieValue(cookieHeader, 'session_expires');
					if (String.isNotBlank(expiresValue)) {
						Long expiresInSeconds = Long.valueOf(expiresValue);
						this.sessionExpires = System.now().addSeconds(Integer.valueOf(expiresInSeconds));
					}
				}
			}
		}
	}

	/**
	 * GET request to the API
	 */
	global String getPath(String path, Map<String, Object> args) {
		if (String.isBlank(path)) {
			throw new TicketureStaffAPIException('Path is required.');
		}

		String fullEndpoint = this.cmsEndpoint + '/api/' + path;

		if (args != null && !args.isEmpty()) {
			fullEndpoint += '?';
			for (String k : args.keySet()) {
				fullEndpoint += k + '=' + String.valueOf(args.get(k)) + '&';
			}
			fullEndpoint = fullEndpoint.left(fullEndpoint.length() - 1);
		}

		HttpResponse theRawResponse = doCallout(null, fullEndpoint, 'GET', null);

		lastBodyReceived = theRawResponse.getBody();

		if (theRawResponse.getStatusCode() != 200) {
			throw new TicketureStaffAPIException('Received status code ' + theRawResponse.getStatusCode() +
					' from the API, with the response: ' + theRawResponse.getBody());
		}

		return theRawResponse.getBody();
	}

	/**
	 * POST request to the API
	 */
	global String postPath(String path, String requestBody) {
		return sendRequest(path, requestBody, 'POST');
	}

	/**
	 * PUT request to the API
	 */
	global String putPath(String path, String requestBody) {
		return sendRequest(path, requestBody, 'PUT');
	}

	/**
	 * PATCH request to the API
	 */
	global String patchPath(String path, String requestBody) {
		return sendRequest(path, requestBody, 'PATCH');
	}

	/**
	 * Generic method to send requests with different HTTP methods
	 */
	private String sendRequest(String path, String requestBody, String httpVerb) {
		this.lastRequestSubmitted = requestBody;

		if (String.isBlank(path)) {
			throw new TicketureStaffAPIException('Path is required.');
		}

		String fullEndpoint = this.cmsEndpoint + '/api/' + path;

		HttpResponse theRawResponse = doCallout(requestBody, fullEndpoint, httpVerb, null);

		this.lastBodyReceived = theRawResponse.getBody();

		if (theRawResponse.getStatusCode() != 200) {
			Map<String, Object> jsonError;
			try {
				jsonError = (Map<String, Object>) JSON.deserializeUntyped(theRawResponse.getBody());
			} catch (Exception e) {
				jsonError = new Map<String, Object>();
			}
			jsonError.put('HTTPStatusCode', theRawResponse.getStatusCode());

			throw new TicketureStaffAPIException(JSON.serialize(jsonError));
		}

		return theRawResponse.getBody();
	}

	/**
	 * Clear request/response data for debugging
	 */
	public void clearRequestResponseData() {
		this.requestsResponses = new List<RequestResponse>();
	}

	/**
	 * Get formatted request history for debugging
	 */
	public String getLastRequests() {
		String allRequests = '';

		if (this.requestsResponses != null) {
			Integer i = 1;
			for (RequestResponse rr : this.requestsResponses) {
				HttpRequest hr = rr.request;

				allRequests += 'R E Q U E S T   # ' + i++ + '\n';
				allRequests += hr.getMethod() + ' ' + hr.getEndpoint();
				allRequests += '\nHEADERS:\n';
				allRequests += JSON.serializePretty(rr.requestHeaders);
				allRequests += '\nBODY:\n';
				if (hr.getEndpoint().containsIgnoreCase('/login')) {
					allRequests += 'auth info redacted';
				} else {
					allRequests += hr.getBody();
				}
				allRequests += '\n\n';
			}
		}

		return allRequests;
	}

	/**
	 * Get formatted response history for debugging
	 */
	public String getLastResponses() {
		String allResponses = '';

		if (this.requestsResponses != null) {
			Integer i = 1;
			for (RequestResponse rr : this.requestsResponses) {
				HttpResponse hr = rr.response;
				allResponses += 'R E S P O N S E   # ' + i++;
				allResponses += '\nSTATUS: ' + hr.getStatusCode() + ' ' + hr.getStatus();
				allResponses += '\nHEADERS:\n';
				Map<String, String> headsClean = new Map<String, String>();
				for (String headerKey : hr.getHeaderKeys()) {
					headsClean.put(headerKey, hr.getHeader(headerKey));
				}
				allResponses += JSON.serializePretty(headsClean);
				allResponses += '\nBODY:\n';
				if (rr.request.getEndpoint().containsIgnoreCase('/login')) {
					allResponses += 'auth info redacted';
				} else {
					allResponses += hr.getBody();
				}
				allResponses += '\n\n';
			}
		}

		return allResponses;
	}

	/**
	 * Helper class to store request/response pairs for debugging
	 */
	private class RequestResponse {
		private HttpRequest request;
		private HttpResponse response;
		private Map<String, String> requestHeaders;
	}

	/**
	 * Custom exception class
	 */
	public class TicketureStaffAPIException extends Exception {
	}
}